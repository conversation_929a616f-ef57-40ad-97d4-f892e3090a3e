import { useState } from "react";
import { Button } from "@/components/ui/button";

interface FloatingActionButtonProps {
  onAddTask: () => void;
  onShowFeatures: () => void;
  onShowSearch: () => void;
}

export default function FloatingActionButton({ 
  onAddTask, 
  onShowFeatures, 
  onShowSearch 
}: FloatingActionButtonProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const actions = [
    {
      id: "search",
      icon: "fas fa-search",
      label: "Search",
      color: "bg-purple-500 hover:bg-purple-600",
      onClick: onShowSearch
    },
    {
      id: "features",
      icon: "fas fa-magic",
      label: "Features",
      color: "bg-blue-500 hover:bg-blue-600",
      onClick: onShowFeatures
    },
    {
      id: "add",
      icon: "fas fa-plus",
      label: "Add Task",
      color: "bg-green-500 hover:bg-green-600",
      onClick: onAddTask
    }
  ];

  return (
    <div className="fixed bottom-6 right-6 z-40">
      {/* Action Buttons */}
      {isExpanded && (
        <div className="absolute bottom-16 right-0 space-y-3 slide-up">
          {actions.map((action, index) => (
            <div
              key={action.id}
              className="flex items-center space-x-3"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="glass rounded-lg px-3 py-2 text-white text-sm font-medium opacity-90">
                {action.label}
              </div>
              <Button
                onClick={() => {
                  action.onClick();
                  setIsExpanded(false);
                }}
                className={`w-12 h-12 rounded-full ${action.color} text-white shadow-lg hover:shadow-xl touch-feedback transition-all duration-200`}
                title={action.label}
              >
                <i className={action.icon}></i>
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Main FAB */}
      <Button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-14 h-14 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl touch-feedback transition-all duration-300 ${
          isExpanded ? 'rotate-45' : ''
        }`}
        title="Quick Actions"
      >
        <i className={`fas ${isExpanded ? 'fa-times' : 'fa-plus'} text-xl`}></i>
      </Button>

      {/* Backdrop */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm -z-10 fade-in"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </div>
  );
}
