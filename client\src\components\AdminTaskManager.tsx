import { useState, useEffect } from "react";
import { useAdminRealtimeTasks } from "@/hooks/useRealtimeDatabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";

interface AdminTaskManagerProps {
  onClose: () => void;
}

export default function AdminTaskManager({ onClose }: AdminTaskManagerProps) {
  const { allTasks, allUsers, loading, adminDeleteTask, adminUpdateTask, getUserById } = useAdminRealtimeTasks();
  const { toast } = useToast();
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [editMode, setEditMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: "medium",
    status: "active",
    dueDate: "",
  });

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [onClose]);

  const handleDeleteTask = async (task: any) => {
    if (!confirm(`Are you sure you want to permanently delete "${task.title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await adminDeleteTask(task.id, task);
      toast({
        title: "Task Deleted",
        description: `Task "${task.title}" has been permanently deleted by admin`,
      });
      setSelectedTask(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditTask = (task: any) => {
    setSelectedTask(task);
    setEditMode(true);
    setFormData({
      title: task.title || "",
      description: task.description || "",
      priority: task.priority || "medium",
      status: task.status || "active",
      dueDate: task.dueDate ? (task.dueDate.toDate ? task.dueDate.toDate().toISOString().split('T')[0] : task.dueDate.split('T')[0]) : "",
    });
  };

  const handleUpdateTask = async () => {
    if (!selectedTask) return;

    try {
      const updates = {
        title: formData.title,
        description: formData.description,
        priority: formData.priority,
        status: formData.status,
        dueDate: formData.dueDate ? new Date(formData.dueDate) : null,
      };

      await adminUpdateTask(selectedTask.id, updates, selectedTask);
      toast({
        title: "Task Updated",
        description: `Task "${formData.title}" has been updated by admin`,
      });
      setEditMode(false);
      setSelectedTask(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredTasks = allTasks.filter((task) => {
    const matchesSearch = task.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === "all" || task.status === filterStatus;
    const matchesPriority = filterPriority === "all" || task.priority === filterPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-500/20 text-red-300 border-red-500/30";
      case "medium": return "bg-blue-500/20 text-blue-300 border-blue-500/30";
      case "low": return "bg-green-500/20 text-green-300 border-green-500/30";
      default: return "bg-gray-500/20 text-gray-300 border-gray-500/30";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500/20 text-green-300 border-green-500/30";
      case "active": return "bg-yellow-500/20 text-yellow-300 border-yellow-500/30";
      default: return "bg-gray-500/20 text-gray-300 border-gray-500/30";
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "No due date";
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  const getUserName = (userId: string) => {
    const user = getUserById(userId);
    return user?.displayName || user?.name || user?.email || "Unknown User";
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="glass rounded-3xl p-8 w-full max-w-6xl h-[80vh]">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-white/10 rounded w-1/3"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="h-32 bg-white/10 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="glass rounded-3xl p-8 w-full max-w-6xl h-[80vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <i className="fas fa-shield-alt mr-3 text-red-400"></i>Admin Task Manager
          </h2>
          <Button
            onClick={onClose}
            variant="ghost"
            className="text-white/60 hover:text-white hover:bg-white/10 p-3 rounded-xl transition-all duration-200"
            title="Close Task Manager"
          >
            <i className="fas fa-times text-xl"></i>
          </Button>
        </div>

        <div className="flex-1 overflow-hidden flex gap-6">
          {/* Tasks List */}
          <div className="flex-1 flex flex-col">
            <div className="glass-dark rounded-2xl p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                <Input
                  placeholder="Search tasks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="glass border-white/20 text-white placeholder-white/60"
                />
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="glass border-white/20 text-white">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filterPriority} onValueChange={setFilterPriority}>
                  <SelectTrigger className="glass border-white/20 text-white">
                    <SelectValue placeholder="Filter by priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priority</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-white/60 text-sm flex items-center">
                  <i className="fas fa-tasks mr-2"></i>
                  {filteredTasks.length} tasks
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto space-y-3">
              {filteredTasks.length === 0 ? (
                <div className="text-center py-12">
                  <i className="fas fa-search text-4xl text-white/20 mb-4"></i>
                  <p className="text-white/60">No tasks found</p>
                  <p className="text-white/40 text-sm">Try adjusting your search or filters</p>
                </div>
              ) : (
                filteredTasks.map((task) => (
                  <div
                    key={task.id}
                    className={`glass hover-glass rounded-2xl p-4 cursor-pointer smooth-transition ${
                      selectedTask?.id === task.id ? "ring-2 ring-[hsl(var(--primary))]" : ""
                    }`}
                    onClick={() => setSelectedTask(task)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-white font-medium">{task.title}</h3>
                      <div className="flex space-x-2">
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditTask(task);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-blue-400 hover:text-blue-300 p-1"
                        >
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTask(task);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-red-400 hover:text-red-300 p-1"
                        >
                          <i className="fas fa-trash"></i>
                        </Button>
                      </div>
                    </div>

                    <p className="text-white/60 text-sm mb-3 line-clamp-2">
                      {task.description || "No description"}
                    </p>

                    <div className="flex items-center space-x-3 text-xs">
                      <span className={`px-2 py-1 rounded border ${getPriorityColor(task.priority)}`}>
                        {task.priority?.toUpperCase()}
                      </span>
                      <span className={`px-2 py-1 rounded border ${getStatusColor(task.status)}`}>
                        {task.status?.toUpperCase()}
                      </span>
                      <span className="text-white/50">
                        <i className="fas fa-user mr-1"></i>
                        {getUserName(task.userId)}
                      </span>
                      <span className="text-white/50">
                        <i className="fas fa-calendar mr-1"></i>
                        {formatDate(task.dueDate)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Task Details/Edit Panel */}
          <div className="w-80 flex-shrink-0">
            {selectedTask ? (
              <div className="glass-dark rounded-2xl p-6 h-full">
                {editMode ? (
                  <>
                    <h3 className="text-lg font-semibold text-white mb-4">Edit Task</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-white/80 text-sm mb-2">Title</label>
                        <Input
                          value={formData.title}
                          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                          className="glass border-white/20 text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-white/80 text-sm mb-2">Description</label>
                        <Textarea
                          value={formData.description}
                          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                          rows={3}
                          className="glass border-white/20 text-white resize-none"
                        />
                      </div>
                      <div>
                        <label className="block text-white/80 text-sm mb-2">Priority</label>
                        <Select value={formData.priority} onValueChange={(value) => setFormData({ ...formData, priority: value })}>
                          <SelectTrigger className="glass border-white/20 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="block text-white/80 text-sm mb-2">Status</label>
                        <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                          <SelectTrigger className="glass border-white/20 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="block text-white/80 text-sm mb-2">Due Date</label>
                        <Input
                          type="date"
                          value={formData.dueDate}
                          onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                          className="glass border-white/20 text-white"
                        />
                      </div>
                    </div>
                    <div className="flex space-x-3 mt-6">
                      <Button
                        onClick={() => setEditMode(false)}
                        variant="ghost"
                        className="flex-1 glass hover:bg-white/15 text-white"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleUpdateTask}
                        className="flex-1 bg-gradient-to-r from-[hsl(var(--primary))] to-[hsl(var(--secondary))] hover:opacity-80 text-white"
                      >
                        Update
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <h3 className="text-lg font-semibold text-white mb-4">Task Details</h3>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-white font-medium">{selectedTask.title}</h4>
                        <p className="text-white/60 text-sm mt-1">
                          {selectedTask.description || "No description"}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <span className="text-white/50 text-xs">Priority</span>
                          <div className={`px-2 py-1 rounded text-xs mt-1 ${getPriorityColor(selectedTask.priority)}`}>
                            {selectedTask.priority?.toUpperCase()}
                          </div>
                        </div>
                        <div>
                          <span className="text-white/50 text-xs">Status</span>
                          <div className={`px-2 py-1 rounded text-xs mt-1 ${getStatusColor(selectedTask.status)}`}>
                            {selectedTask.status?.toUpperCase()}
                          </div>
                        </div>
                      </div>

                      <div>
                        <span className="text-white/50 text-xs">Assigned to</span>
                        <p className="text-white mt-1">{getUserName(selectedTask.userId)}</p>
                      </div>

                      <div>
                        <span className="text-white/50 text-xs">Due Date</span>
                        <p className="text-white mt-1">{formatDate(selectedTask.dueDate)}</p>
                      </div>

                      <div>
                        <span className="text-white/50 text-xs">Created</span>
                        <p className="text-white mt-1">{formatDate(selectedTask.createdAt)}</p>
                      </div>
                    </div>

                    <div className="flex space-x-3 mt-6">
                      <Button
                        onClick={() => handleEditTask(selectedTask)}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <i className="fas fa-edit mr-2"></i>Edit
                      </Button>
                      <Button
                        onClick={() => handleDeleteTask(selectedTask)}
                        className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                      >
                        <i className="fas fa-trash mr-2"></i>Delete
                      </Button>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="glass-dark rounded-2xl p-6 h-full flex items-center justify-center">
                <div className="text-center">
                  <i className="fas fa-mouse-pointer text-4xl text-white/20 mb-4"></i>
                  <p className="text-white/60">Select a task to view details</p>
                  <p className="text-white/40 text-sm mt-1">Click on any task to manage it</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}