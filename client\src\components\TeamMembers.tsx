import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { collection, onSnapshot, query, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";

interface TeamMember {
  id: string;
  displayName: string;
  email: string;
  photoURL?: string;
  lastSeen: any;
  isOnline: boolean;
  role: string;
  tasksCompleted: number;
}

export default function TeamMembers() {
  const { user } = useAuth();
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterRole, setFilterRole] = useState("all");

  // Mock data for demonstration
  const mockMembers: TeamMember[] = [
    {
      id: "1",
      displayName: "<PERSON>",
      email: "<EMAIL>",
      isOnline: true,
      lastSeen: new Date(),
      role: "Manager",
      tasksCompleted: 32
    },
    {
      id: "2",
      displayName: "<PERSON>",
      email: "<EMAIL>",
      isOnline: true,
      lastSeen: new Date(Date.now() - 180000), // 3 minutes ago
      role: "Lead",
      tasksCompleted: 28
    },
    {
      id: "3",
      displayName: "Jordan Kim",
      email: "<EMAIL>",
      isOnline: false,
      lastSeen: new Date(Date.now() - 2700000), // 45 minutes ago
      role: "Member",
      tasksCompleted: 19
    },
    {
      id: "4",
      displayName: "Sam Taylor",
      email: "<EMAIL>",
      isOnline: true,
      lastSeen: new Date(Date.now() - 60000), // 1 minute ago
      role: "Member",
      tasksCompleted: 16
    },
    {
      id: "5",
      displayName: "Casey Morgan",
      email: "<EMAIL>",
      isOnline: false,
      lastSeen: new Date(Date.now() - 7200000), // 2 hours ago
      role: "Member",
      tasksCompleted: 14
    },
    {
      id: "6",
      displayName: "Riley Chen",
      email: "<EMAIL>",
      isOnline: true,
      lastSeen: new Date(),
      role: "Lead",
      tasksCompleted: 22
    }
  ];

  useEffect(() => {
    if (!user) return;

    // Add current user to mock data if not already present
    const currentUserMember: TeamMember = {
      id: user.uid,
      displayName: user.displayName || user.email?.split('@')[0] || "You",
      email: user.email || "",
      photoURL: user.photoURL,
      isOnline: true,
      lastSeen: new Date(),
      role: "Admin", // Current user is admin
      tasksCompleted: 5
    };

    // Try to fetch real users from Firestore
    const membersQuery = query(
      collection(db, "users"),
      orderBy("lastSeen", "desc")
    );

    const unsubscribe = onSnapshot(membersQuery, (snapshot) => {
      const membersList: TeamMember[] = [];

      // Add current user first
      membersList.push(currentUserMember);

      snapshot.forEach((doc) => {
        const data = doc.data();
        // Don't duplicate current user
        if (doc.id !== user.uid) {
          membersList.push({
            id: doc.id,
            displayName: data.displayName || data.email?.split('@')[0] || "Unknown User",
            email: data.email || "",
            photoURL: data.photoURL,
            lastSeen: data.lastSeen,
            isOnline: data.isOnline || false,
            role: data.role || "Member",
            tasksCompleted: data.tasksCompleted || 0
          });
        }
      });

      // If no other users found, use mock data
      if (membersList.length === 1) {
        // Add mock members but exclude any that match current user email
        const filteredMockMembers = mockMembers.filter(m => m.email !== user.email);
        membersList.push(...filteredMockMembers);
      }

      setMembers(membersList);
      setLoading(false);
    }, (error) => {
      console.log("Firestore error, using mock data:", error);
      // If Firestore fails, use mock data
      const allMembers = [currentUserMember, ...mockMembers.filter(m => m.email !== user.email)];
      setMembers(allMembers);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [user]);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getLastSeenText = (lastSeen: any) => {
    if (!lastSeen) return "Never";

    let date: Date;
    if (lastSeen.toDate) {
      date = lastSeen.toDate();
    } else if (lastSeen instanceof Date) {
      date = lastSeen;
    } else {
      date = new Date(lastSeen);
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "admin":
        return "bg-red-500/20 text-red-300";
      case "manager":
        return "bg-purple-500/20 text-purple-300";
      case "lead":
        return "bg-blue-500/20 text-blue-300";
      default:
        return "bg-green-500/20 text-green-300";
    }
  };

  // Filter members based on search term and role
  const filteredMembers = members.filter(member => {
    const matchesSearch = member.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === "all" || member.role.toLowerCase() === filterRole.toLowerCase();
    return matchesSearch && matchesRole;
  });

  if (loading) {
    return (
      <div className="glass rounded-3xl p-6 slide-up">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <i className="fas fa-users mr-2 text-blue-400"></i>
          Team Members
        </h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/10 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-white/10 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-white/10 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="glass rounded-3xl p-6 slide-up">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-white flex items-center">
          <i className="fas fa-users mr-2 text-blue-400 pulse-glow"></i>
          Team Members
        </h3>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-white/60 text-sm">{members.filter(m => m.isOnline).length} online</span>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="mb-4 space-y-3">
        <div className="relative">
          <input
            type="text"
            placeholder="Search members..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full glass border-white/20 text-white placeholder-white/60 rounded-lg px-4 py-2 pl-10 text-sm focus:ring-blue-500/30 focus:border-blue-500/40"
          />
          <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"></i>
        </div>

        <div className="flex space-x-2 overflow-x-auto">
          {["all", "admin", "manager", "lead", "member"].map((role) => (
            <button
              key={role}
              onClick={() => setFilterRole(role)}
              className={`px-3 py-1 rounded-full text-xs whitespace-nowrap touch-feedback transition-all duration-200 ${
                filterRole === role
                  ? "bg-blue-500/30 text-blue-300 border border-blue-500/50"
                  : "glass hover:bg-white/10 text-white/70 border border-white/20"
              }`}
            >
              {role.charAt(0).toUpperCase() + role.slice(1)}
            </button>
          ))}
        </div>

        {/* Member Count */}
        <div className="text-white/60 text-xs">
          Showing {filteredMembers.length} of {members.length} members
        </div>
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {filteredMembers.length === 0 ? (
          <div className="text-center py-8">
            <i className="fas fa-user-friends text-4xl text-white/20 mb-4"></i>
            <p className="text-white/60">
              {searchTerm || filterRole !== "all" ? "No members match your search" : "No team members found"}
            </p>
            <p className="text-white/40 text-sm">
              {searchTerm || filterRole !== "all" ? "Try adjusting your filters" : "Invite colleagues to collaborate"}
            </p>
          </div>
        ) : (
          filteredMembers.map((member) => (
            <div
              key={member.id}
              className="glass-dark rounded-xl p-4 hover:bg-white/10 transition-all duration-200 touch-feedback"
            >
              <div className="flex items-center space-x-3">
                {/* Avatar */}
                <div className="relative">
                  {member.photoURL ? (
                    <img
                      src={member.photoURL}
                      alt={member.displayName}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                      <span className="text-white font-semibold text-sm">
                        {getInitials(member.displayName)}
                      </span>
                    </div>
                  )}
                  {/* Online Status */}
                  <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-gray-800 ${
                    member.isOnline ? 'bg-green-400' : 'bg-gray-500'
                  }`}></div>
                </div>

                {/* Member Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-white font-medium truncate">
                      {member.displayName}
                    </h4>
                    {member.id === user?.uid && (
                      <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">
                        You
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`text-xs px-2 py-1 rounded-full ${getRoleColor(member.role)}`}>
                      {member.role}
                    </span>
                    <span className="text-white/50 text-xs">
                      {member.isOnline ? "Online" : getLastSeenText(member.lastSeen)}
                    </span>
                  </div>
                </div>

                {/* Stats */}
                <div className="text-right">
                  <div className="text-white font-bold text-sm">
                    {member.tasksCompleted}
                  </div>
                  <div className="text-white/50 text-xs">
                    tasks done
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-1">
                  <button className="w-8 h-8 glass rounded-lg flex items-center justify-center hover:bg-white/10 touch-feedback">
                    <i className="fas fa-comment text-white/60 text-sm"></i>
                  </button>
                  <button className="w-8 h-8 glass rounded-lg flex items-center justify-center hover:bg-white/10 touch-feedback">
                    <i className="fas fa-ellipsis-v text-white/60 text-sm"></i>
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Quick Actions */}
      <div className="mt-4 pt-4 border-t border-white/10">
        <div className="flex space-x-2">
          <button className="flex-1 glass rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
            <i className="fas fa-user-plus text-blue-400 mb-1"></i>
            <div className="text-white text-xs">Invite</div>
          </button>
          <button className="flex-1 glass rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
            <i className="fas fa-users-cog text-green-400 mb-1"></i>
            <div className="text-white text-xs">Manage</div>
          </button>
          <button className="flex-1 glass rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
            <i className="fas fa-chart-line text-purple-400 mb-1"></i>
            <div className="text-white text-xs">Analytics</div>
          </button>
        </div>
      </div>
    </div>
  );
}
