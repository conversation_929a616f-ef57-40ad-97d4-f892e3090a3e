import { useState, useEffect } from "react";
import { getConnectionStatus } from "@/lib/firebase";
import { getOfflineMode } from "@/lib/offlineMode";

export default function ConnectionStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [showStatus, setShowStatus] = useState(false);
  const [offlineMode, setOfflineMode] = useState({ isOffline: false, reason: '' });

  useEffect(() => {
    const checkConnection = () => {
      const online = navigator.onLine && getConnectionStatus();
      const offlineModeStatus = getOfflineMode();

      setIsOnline(online && !offlineModeStatus.isOffline);
      setOfflineMode(offlineModeStatus);
      setShowStatus(!online || offlineModeStatus.isOffline);
    };

    // Initial check
    checkConnection();

    // Listen for network changes
    window.addEventListener('online', checkConnection);
    window.addEventListener('offline', checkConnection);

    // Check offline mode status periodically
    const statusInterval = setInterval(checkConnection, 2000);

    // Auto-hide status after 5 seconds when back online
    let hideTimer: NodeJS.Timeout;
    if (isOnline && !offlineMode.isOffline && showStatus) {
      hideTimer = setTimeout(() => setShowStatus(false), 5000);
    }

    return () => {
      window.removeEventListener('online', checkConnection);
      window.removeEventListener('offline', checkConnection);
      clearInterval(statusInterval);
      if (hideTimer) clearTimeout(hideTimer);
    };
  }, [isOnline, showStatus, offlineMode.isOffline]);

  if (!showStatus) return null;

  return (
    <div className={`fixed top-20 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-lg shadow-lg transition-all duration-300 max-w-sm ${
      isOnline
        ? 'bg-green-500/90 text-white'
        : 'bg-orange-500/90 text-white'
    }`}>
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          isOnline ? 'bg-green-200 animate-pulse' : 'bg-orange-200'
        }`}></div>
        <div className="flex-1">
          <span className="text-sm font-medium block">
            {isOnline ? 'Connection restored' : 'Offline Mode'}
          </span>
          {offlineMode.isOffline && offlineMode.reason && (
            <span className="text-xs opacity-80 block">
              {offlineMode.reason}
            </span>
          )}
          {!isOnline && (
            <span className="text-xs opacity-80 block">
              App continues to work with local data
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
