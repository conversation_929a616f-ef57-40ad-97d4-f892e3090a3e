import { useState, useEffect } from "react";
import { getConnectionStatus } from "@/lib/firebase";

export default function ConnectionStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [showStatus, setShowStatus] = useState(false);

  useEffect(() => {
    const checkConnection = () => {
      const online = navigator.onLine && getConnectionStatus();
      setIsOnline(online);
      setShowStatus(!online);
    };

    // Initial check
    checkConnection();

    // Listen for network changes
    window.addEventListener('online', checkConnection);
    window.addEventListener('offline', checkConnection);

    // Auto-hide status after 5 seconds when back online
    let hideTimer: NodeJS.Timeout;
    if (isOnline && showStatus) {
      hideTimer = setTimeout(() => setShowStatus(false), 5000);
    }

    return () => {
      window.removeEventListener('online', checkConnection);
      window.removeEventListener('offline', checkConnection);
      if (hideTimer) clearTimeout(hideTimer);
    };
  }, [isOnline, showStatus]);

  if (!showStatus) return null;

  return (
    <div className={`fixed top-20 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-lg shadow-lg transition-all duration-300 ${
      isOnline 
        ? 'bg-green-500/90 text-white' 
        : 'bg-red-500/90 text-white'
    }`}>
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          isOnline ? 'bg-green-200 animate-pulse' : 'bg-red-200'
        }`}></div>
        <span className="text-sm font-medium">
          {isOnline ? 'Connection restored' : 'Connection lost - Working offline'}
        </span>
      </div>
    </div>
  );
}
