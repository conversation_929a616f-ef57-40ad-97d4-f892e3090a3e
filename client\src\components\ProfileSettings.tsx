import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { updateProfile } from "firebase/auth";
import { doc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";

interface ProfileSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ProfileSettings({ isOpen, onClose }: ProfileSettingsProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [darkMode, setDarkMode] = useState(true);
  const [notifications, setNotifications] = useState(true);
  const [emailUpdates, setEmailUpdates] = useState(false);
  const [autoSave, setAutoSave] = useState(true);
  const [language, setLanguage] = useState("en");
  const [timezone, setTimezone] = useState("UTC");
  
  const [profileData, setProfileData] = useState({
    displayName: user?.displayName || "",
    email: user?.email || "",
    bio: "",
    location: "",
    website: "",
    phone: ""
  });

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem("taskmaster-settings");
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      setDarkMode(settings.darkMode ?? true);
      setNotifications(settings.notifications ?? true);
      setEmailUpdates(settings.emailUpdates ?? false);
      setAutoSave(settings.autoSave ?? true);
      setLanguage(settings.language ?? "en");
      setTimezone(settings.timezone ?? "UTC");
    }
  }, []);

  // Apply dark mode
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
      document.body.style.background = "linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #16213e 100%)";
    } else {
      document.documentElement.classList.remove("dark");
      document.body.style.background = "linear-gradient(135deg, #667eea 0%, #764ba2 100%)";
    }
  }, [darkMode]);

  const saveSettings = () => {
    const settings = {
      darkMode,
      notifications,
      emailUpdates,
      autoSave,
      language,
      timezone
    };
    localStorage.setItem("taskmaster-settings", JSON.stringify(settings));
    toast({
      title: "Settings Saved",
      description: "Your preferences have been updated successfully.",
    });
  };

  const handleProfileUpdate = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Update Firebase Auth profile
      await updateProfile(user, {
        displayName: profileData.displayName,
      });

      // Update Firestore user document
      await updateDoc(doc(db, "users", user.uid), {
        displayName: profileData.displayName,
        bio: profileData.bio,
        location: profileData.location,
        website: profileData.website,
        phone: profileData.phone,
        updatedAt: new Date(),
      });

      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 fade-in"
      onClick={onClose}
    >
      <div 
        className="glass rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden bounce-in"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <i className="fas fa-user-cog mr-3 text-blue-400 pulse-glow"></i>
            Profile & Settings
          </h2>
          <button
            onClick={onClose}
            className="close-btn touch-target"
            title="Close Settings"
            aria-label="Close Settings"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Sidebar */}
          <div className="w-64 border-r border-white/10 p-4">
            <div className="space-y-2">
              <button className="w-full text-left px-3 py-2 text-white bg-white/20 rounded-lg flex items-center space-x-3">
                <i className="fas fa-user"></i>
                <span>Profile</span>
              </button>
              <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg flex items-center space-x-3">
                <i className="fas fa-cog"></i>
                <span>Preferences</span>
              </button>
              <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg flex items-center space-x-3">
                <i className="fas fa-bell"></i>
                <span>Notifications</span>
              </button>
              <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg flex items-center space-x-3">
                <i className="fas fa-shield-alt"></i>
                <span>Privacy</span>
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="space-y-8">
              {/* Profile Section */}
              <div>
                <h3 className="text-xl font-bold text-white mb-4">Profile Information</h3>
                <div className="glass-dark rounded-xl p-6">
                  <div className="flex items-center space-x-6 mb-6">
                    {/* Avatar */}
                    <div className="relative">
                      {user?.photoURL ? (
                        <img
                          src={user.photoURL}
                          alt="Profile"
                          className="w-20 h-20 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                          <span className="text-white font-bold text-2xl">
                            {getInitials(profileData.displayName || "U")}
                          </span>
                        </div>
                      )}
                      <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white hover:bg-blue-600 touch-feedback">
                        <i className="fas fa-camera text-sm"></i>
                      </button>
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="text-white font-bold text-lg">{profileData.displayName || "User"}</h4>
                      <p className="text-white/60">{profileData.email}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <span className="text-green-400 text-sm">Online</span>
                        </div>
                        <span className="text-white/50 text-sm">Member since {new Date(user?.metadata.creationTime || "").toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-white/80 text-sm mb-2">Display Name</label>
                      <Input
                        value={profileData.displayName}
                        onChange={(e) => setProfileData({...profileData, displayName: e.target.value})}
                        className="glass border-white/20 text-white placeholder-white/60"
                        placeholder="Your display name"
                      />
                    </div>
                    <div>
                      <label className="block text-white/80 text-sm mb-2">Email</label>
                      <Input
                        value={profileData.email}
                        disabled
                        className="glass border-white/20 text-white/60 bg-white/5"
                      />
                    </div>
                    <div>
                      <label className="block text-white/80 text-sm mb-2">Location</label>
                      <Input
                        value={profileData.location}
                        onChange={(e) => setProfileData({...profileData, location: e.target.value})}
                        className="glass border-white/20 text-white placeholder-white/60"
                        placeholder="Your location"
                      />
                    </div>
                    <div>
                      <label className="block text-white/80 text-sm mb-2">Phone</label>
                      <Input
                        value={profileData.phone}
                        onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                        className="glass border-white/20 text-white placeholder-white/60"
                        placeholder="Your phone number"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-white/80 text-sm mb-2">Bio</label>
                      <textarea
                        value={profileData.bio}
                        onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                        className="w-full glass border-white/20 text-white placeholder-white/60 rounded-lg p-3 resize-none"
                        rows={3}
                        placeholder="Tell us about yourself..."
                      />
                    </div>
                  </div>

                  <div className="flex justify-end mt-6">
                    <Button
                      onClick={handleProfileUpdate}
                      disabled={loading}
                      className="bg-blue-500 hover:bg-blue-600 text-white touch-feedback"
                    >
                      {loading ? "Updating..." : "Update Profile"}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Settings Section */}
              <div>
                <h3 className="text-xl font-bold text-white mb-4">Preferences</h3>
                <div className="glass-dark rounded-xl p-6">
                  <div className="space-y-6">
                    {/* Dark Mode */}
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-white font-medium">Dark Mode</h4>
                        <p className="text-white/60 text-sm">Use dark theme for better night viewing</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={darkMode}
                          onChange={(e) => setDarkMode(e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    {/* Notifications */}
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-white font-medium">Push Notifications</h4>
                        <p className="text-white/60 text-sm">Receive notifications for important updates</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={notifications}
                          onChange={(e) => setNotifications(e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    {/* Auto Save */}
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-white font-medium">Auto Save</h4>
                        <p className="text-white/60 text-sm">Automatically save your work</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={autoSave}
                          onChange={(e) => setAutoSave(e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        onClick={saveSettings}
                        className="bg-green-500 hover:bg-green-600 text-white touch-feedback"
                      >
                        <i className="fas fa-save mr-2"></i>
                        Save Settings
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
