import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

interface UserPreferencesProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function UserPreferences({ isOpen, onClose }: UserPreferencesProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("preferences");

  // Preferences
  const [darkMode, setDarkMode] = useState(true);
  const [autoSave, setAutoSave] = useState(true);
  const [language, setLanguage] = useState("en");
  const [timezone, setTimezone] = useState("UTC");
  const [taskReminders, setTaskReminders] = useState(true);
  const [compactView, setCompactView] = useState(false);

  // Notifications
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(false);
  const [taskDeadlines, setTaskDeadlines] = useState(true);
  const [teamUpdates, setTeamUpdates] = useState(true);
  const [adminMessages, setAdminMessages] = useState(true);
  const [weeklyReports, setWeeklyReports] = useState(false);

  // Privacy
  const [profileVisibility, setProfileVisibility] = useState("team");
  const [activityStatus, setActivityStatus] = useState(true);
  const [dataSharing, setDataSharing] = useState(false);
  const [analyticsTracking, setAnalyticsTracking] = useState(true);

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem("taskmaster-settings");
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      // Preferences
      setDarkMode(settings.darkMode ?? true);
      setAutoSave(settings.autoSave ?? true);
      setLanguage(settings.language ?? "en");
      setTimezone(settings.timezone ?? "UTC");
      setTaskReminders(settings.taskReminders ?? true);
      setCompactView(settings.compactView ?? false);

      // Notifications
      setPushNotifications(settings.pushNotifications ?? true);
      setEmailNotifications(settings.emailNotifications ?? false);
      setTaskDeadlines(settings.taskDeadlines ?? true);
      setTeamUpdates(settings.teamUpdates ?? true);
      setAdminMessages(settings.adminMessages ?? true);
      setWeeklyReports(settings.weeklyReports ?? false);

      // Privacy
      setProfileVisibility(settings.profileVisibility ?? "team");
      setActivityStatus(settings.activityStatus ?? true);
      setDataSharing(settings.dataSharing ?? false);
      setAnalyticsTracking(settings.analyticsTracking ?? true);
    }
  }, []);

  // Apply dark mode
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
      document.body.style.background = "linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #16213e 100%)";
    } else {
      document.documentElement.classList.remove("dark");
      document.body.style.background = "linear-gradient(135deg, #667eea 0%, #764ba2 100%)";
    }
  }, [darkMode]);

  const saveSettings = () => {
    const settings = {
      // Preferences
      darkMode,
      autoSave,
      language,
      timezone,
      taskReminders,
      compactView,

      // Notifications
      pushNotifications,
      emailNotifications,
      taskDeadlines,
      teamUpdates,
      adminMessages,
      weeklyReports,

      // Privacy
      profileVisibility,
      activityStatus,
      dataSharing,
      analyticsTracking
    };

    localStorage.setItem("taskmaster-settings", JSON.stringify(settings));
    toast({
      title: "Settings Saved ✅",
      description: "Your preferences have been updated successfully.",
    });
  };

  const resetToDefaults = () => {
    // Reset Preferences
    setDarkMode(true);
    setAutoSave(true);
    setLanguage("en");
    setTimezone("UTC");
    setTaskReminders(true);
    setCompactView(false);

    // Reset Notifications
    setPushNotifications(true);
    setEmailNotifications(false);
    setTaskDeadlines(true);
    setTeamUpdates(true);
    setAdminMessages(true);
    setWeeklyReports(false);

    // Reset Privacy
    setProfileVisibility("team");
    setActivityStatus(true);
    setDataSharing(false);
    setAnalyticsTracking(true);

    toast({
      title: "Reset Complete",
      description: "All settings have been reset to defaults.",
    });
  };

  if (!isOpen) return null;

  const renderToggle = (checked: boolean, onChange: (checked: boolean) => void) => (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className="sr-only peer"
      />
      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
    </label>
  );

  return (
    <div
      className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 fade-in"
      onClick={onClose}
    >
      <div
        className="glass rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden bounce-in"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <i className="fas fa-cog mr-3 text-blue-400 pulse-glow"></i>
            User Settings
          </h2>
          <button
            onClick={onClose}
            className="close-btn touch-target"
            title="Close Settings"
            aria-label="Close Settings"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Sidebar */}
          <div className="w-64 border-r border-white/10 p-4">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab("preferences")}
                className={`w-full text-left px-3 py-2 rounded-lg flex items-center space-x-3 transition-all duration-200 ${
                  activeTab === "preferences"
                    ? "text-white bg-white/20"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                }`}
              >
                <i className="fas fa-cog"></i>
                <span>Preferences</span>
              </button>
              <button
                onClick={() => setActiveTab("notifications")}
                className={`w-full text-left px-3 py-2 rounded-lg flex items-center space-x-3 transition-all duration-200 ${
                  activeTab === "notifications"
                    ? "text-white bg-white/20"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                }`}
              >
                <i className="fas fa-bell"></i>
                <span>Notifications</span>
              </button>
              <button
                onClick={() => setActiveTab("privacy")}
                className={`w-full text-left px-3 py-2 rounded-lg flex items-center space-x-3 transition-all duration-200 ${
                  activeTab === "privacy"
                    ? "text-white bg-white/20"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                }`}
              >
                <i className="fas fa-shield-alt"></i>
                <span>Privacy</span>
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Preferences Tab */}
            {activeTab === "preferences" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <i className="fas fa-cog mr-2 text-blue-400"></i>
                  Preferences
                </h3>

                <div className="glass-dark rounded-xl p-6">
                  <div className="space-y-6">
                    {/* Appearance */}
                    <div>
                      <h4 className="text-white font-semibold mb-4">Appearance</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Dark Mode</h5>
                            <p className="text-white/60 text-sm">Use dark theme for better night viewing</p>
                          </div>
                          {renderToggle(darkMode, setDarkMode)}
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Compact View</h5>
                            <p className="text-white/60 text-sm">Show more content in less space</p>
                          </div>
                          {renderToggle(compactView, setCompactView)}
                        </div>
                      </div>
                    </div>

                    {/* Functionality */}
                    <div className="border-t border-white/10 pt-6">
                      <h4 className="text-white font-semibold mb-4">Functionality</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Auto Save</h5>
                            <p className="text-white/60 text-sm">Automatically save your work</p>
                          </div>
                          {renderToggle(autoSave, setAutoSave)}
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Task Reminders</h5>
                            <p className="text-white/60 text-sm">Get reminders for upcoming tasks</p>
                          </div>
                          {renderToggle(taskReminders, setTaskReminders)}
                        </div>
                      </div>
                    </div>

                    {/* Language & Region */}
                    <div className="border-t border-white/10 pt-6">
                      <h4 className="text-white font-semibold mb-4">Language & Region</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-white/80 text-sm mb-2">Language</label>
                          <select
                            value={language}
                            onChange={(e) => setLanguage(e.target.value)}
                            className="w-full glass border-white/20 text-white bg-transparent rounded-lg px-3 py-2"
                          >
                            <option value="en" className="bg-gray-800">English</option>
                            <option value="es" className="bg-gray-800">Español</option>
                            <option value="fr" className="bg-gray-800">Français</option>
                            <option value="de" className="bg-gray-800">Deutsch</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-white/80 text-sm mb-2">Timezone</label>
                          <select
                            value={timezone}
                            onChange={(e) => setTimezone(e.target.value)}
                            className="w-full glass border-white/20 text-white bg-transparent rounded-lg px-3 py-2"
                          >
                            <option value="UTC" className="bg-gray-800">UTC</option>
                            <option value="America/New_York" className="bg-gray-800">Eastern Time</option>
                            <option value="America/Los_Angeles" className="bg-gray-800">Pacific Time</option>
                            <option value="Europe/London" className="bg-gray-800">London</option>
                            <option value="Asia/Tokyo" className="bg-gray-800">Tokyo</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === "notifications" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <i className="fas fa-bell mr-2 text-yellow-400"></i>
                  Notifications
                </h3>

                <div className="glass-dark rounded-xl p-6">
                  <div className="space-y-6">
                    {/* Push Notifications */}
                    <div>
                      <h4 className="text-white font-semibold mb-4">Push Notifications</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Enable Push Notifications</h5>
                            <p className="text-white/60 text-sm">Receive real-time notifications</p>
                          </div>
                          {renderToggle(pushNotifications, setPushNotifications)}
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Task Deadlines</h5>
                            <p className="text-white/60 text-sm">Get notified about upcoming deadlines</p>
                          </div>
                          {renderToggle(taskDeadlines, setTaskDeadlines)}
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Team Updates</h5>
                            <p className="text-white/60 text-sm">Notifications about team activities</p>
                          </div>
                          {renderToggle(teamUpdates, setTeamUpdates)}
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Admin Messages</h5>
                            <p className="text-white/60 text-sm">Important messages from administrators</p>
                          </div>
                          {renderToggle(adminMessages, setAdminMessages)}
                        </div>
                      </div>
                    </div>

                    {/* Email Notifications */}
                    <div className="border-t border-white/10 pt-6">
                      <h4 className="text-white font-semibold mb-4">Email Notifications</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Email Notifications</h5>
                            <p className="text-white/60 text-sm">Receive notifications via email</p>
                          </div>
                          {renderToggle(emailNotifications, setEmailNotifications)}
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Weekly Reports</h5>
                            <p className="text-white/60 text-sm">Get weekly summary reports</p>
                          </div>
                          {renderToggle(weeklyReports, setWeeklyReports)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Privacy Tab */}
            {activeTab === "privacy" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <i className="fas fa-shield-alt mr-2 text-green-400"></i>
                  Privacy
                </h3>

                <div className="glass-dark rounded-xl p-6">
                  <div className="space-y-6">
                    {/* Profile Visibility */}
                    <div>
                      <h4 className="text-white font-semibold mb-4">Profile Visibility</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-white/80 text-sm mb-2">Who can see your profile</label>
                          <select
                            value={profileVisibility}
                            onChange={(e) => setProfileVisibility(e.target.value)}
                            className="w-full glass border-white/20 text-white bg-transparent rounded-lg px-3 py-2"
                          >
                            <option value="public" className="bg-gray-800">Everyone</option>
                            <option value="team" className="bg-gray-800">Team Members Only</option>
                            <option value="private" className="bg-gray-800">Private</option>
                          </select>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Show Activity Status</h5>
                            <p className="text-white/60 text-sm">Let others see when you're online</p>
                          </div>
                          {renderToggle(activityStatus, setActivityStatus)}
                        </div>
                      </div>
                    </div>

                    {/* Data & Analytics */}
                    <div className="border-t border-white/10 pt-6">
                      <h4 className="text-white font-semibold mb-4">Data & Analytics</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Data Sharing</h5>
                            <p className="text-white/60 text-sm">Share anonymized data to improve the service</p>
                          </div>
                          {renderToggle(dataSharing, setDataSharing)}
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-white font-medium">Analytics Tracking</h5>
                            <p className="text-white/60 text-sm">Allow usage analytics for better experience</p>
                          </div>
                          {renderToggle(analyticsTracking, setAnalyticsTracking)}
                        </div>
                      </div>
                    </div>

                    {/* Data Management */}
                    <div className="border-t border-white/10 pt-6">
                      <h4 className="text-white font-semibold mb-4">Data Management</h4>
                      <div className="space-y-3">
                        <Button
                          onClick={() => {
                            toast({
                              title: "Export Requested",
                              description: "Your data export will be ready shortly.",
                            });
                          }}
                          className="w-full glass hover:bg-white/10 text-white border border-white/20"
                          variant="outline"
                        >
                          <i className="fas fa-download mr-2"></i>
                          Export My Data
                        </Button>

                        <Button
                          onClick={resetToDefaults}
                          className="w-full glass hover:bg-white/10 text-white border border-white/20"
                          variant="outline"
                        >
                          <i className="fas fa-undo mr-2"></i>
                          Reset to Defaults
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Save Button */}
            <div className="flex justify-end space-x-3 mt-8">
              <Button
                onClick={saveSettings}
                className="bg-blue-500 hover:bg-blue-600 text-white touch-feedback"
              >
                <i className="fas fa-save mr-2"></i>
                Save Settings
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
