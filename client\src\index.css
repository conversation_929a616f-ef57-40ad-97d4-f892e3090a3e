@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --background: 210 11% 98%;
  --foreground: 240 10% 4%;
  --muted: 210 11% 96%;
  --muted-foreground: 215 16% 47%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 4%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 4%;
  --border: 215 28% 92%;
  --input: 215 28% 92%;
  --primary: 247 84% 67%;
  --primary-foreground: 211 100% 99%;
  --secondary: 285 84% 67%;
  --secondary-foreground: 24 9% 10%;
  --accent: 210 11% 96%;
  --accent-foreground: 24 9% 10%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 60 9% 98%;
  --ring: 247 84% 67%;
  --radius: 0.75rem;
  
  /* Glass morphism colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-dark-bg: rgba(0, 0, 0, 0.2);
  --glass-dark-border: rgba(255, 255, 255, 0.1);
}

.dark {
  --background: 240 10% 4%;
  --foreground: 0 0% 98%;
  --muted: 240 4% 16%;
  --muted-foreground: 240 5% 65%;
  --popover: 240 10% 4%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 4%;
  --card-foreground: 0 0% 98%;
  --border: 240 4% 16%;
  --input: 240 4% 16%;
  --primary: 247 84% 67%;
  --primary-foreground: 211 100% 99%;
  --secondary: 240 4% 16%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 4% 16%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 0 0% 98%;
  --ring: 247 84% 67%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
  }
  
  .glass-dark {
    background: var(--glass-dark-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-dark-border);
    border-radius: 8px;
  }
  
  .blocky {
    border-radius: 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  .blocky-card {
    border-radius: 12px !important;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  }
  
  .gradient-bg-alt {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 50%, #f093fb 100%);
  }
  
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-glass:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }
}
