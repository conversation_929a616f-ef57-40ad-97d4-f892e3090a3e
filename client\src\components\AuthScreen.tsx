import { useState } from "react";
import { signInWithGoogle, signUpWithEmail, signInWithEmail } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

export default function AuthScreen() {
  const [loading, setLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    displayName: ""
  });
  const { toast } = useToast();

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      await signInWithGoogle();
    } catch (error: any) {
      toast({
        title: "Authentication Error",
        description: error.message || "Failed to sign in with Google. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.email || !formData.password) return;
    if (isSignUp && !formData.displayName) return;

    try {
      setLoading(true);
      if (isSignUp) {
        await signUpWithEmail(formData.email, formData.password, formData.displayName);
        toast({
          title: "Account Created!",
          description: "Welcome to TaskFlow Pro! You can now start managing your tasks.",
        });
      } else {
        await signInWithEmail(formData.email, formData.password);
        toast({
          title: "Welcome Back!",
          description: "Successfully signed in to TaskFlow Pro.",
        });
      }
    } catch (error: any) {
      let errorMessage = "An error occurred. Please try again.";
      
      if (error.code === "auth/email-already-in-use") {
        errorMessage = "This email is already registered. Please sign in instead.";
      } else if (error.code === "auth/weak-password") {
        errorMessage = "Password should be at least 6 characters long.";
      } else if (error.code === "auth/invalid-email") {
        errorMessage = "Please enter a valid email address.";
      } else if (error.code === "auth/user-not-found") {
        errorMessage = "No account found with this email. Please sign up first.";
      } else if (error.code === "auth/wrong-password") {
        errorMessage = "Incorrect password. Please try again.";
      }

      toast({
        title: isSignUp ? "Sign Up Failed" : "Sign In Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 gradient-bg-alt">
      <div className="glass blocky-card p-8 w-full max-w-md smooth-transition">
        <div className="text-center mb-8">
          <div className="w-16 h-16 glass blocky flex items-center justify-center mx-auto mb-4">
            <i className="fas fa-tasks text-2xl text-white"></i>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Cyber Community Task</h1>
          <p className="text-white/70">Advanced Team Collaboration Platform</p>
        </div>

        <form onSubmit={handleEmailAuth} className="space-y-4 mb-6">
          {isSignUp && (
            <Input
              type="text"
              placeholder="Full Name"
              value={formData.displayName}
              onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
              className="glass border-white/20 text-white placeholder-white/60 focus:ring-white/30"
              required
            />
          )}
          <Input
            type="email"
            placeholder="Email Address"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            className="glass border-white/20 text-white placeholder-white/60 focus:ring-white/30"
            required
          />
          <Input
            type="password"
            placeholder="Password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            className="glass border-white/20 text-white placeholder-white/60 focus:ring-white/30"
            required
            minLength={6}
          />
          
          <Button
            type="submit"
            className="w-full bg-white/20 hover:bg-white/30 text-white font-semibold backdrop-blur-sm"
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                {isSignUp ? "Creating Account..." : "Signing In..."}
              </div>
            ) : (
              isSignUp ? "Create Account" : "Sign In"
            )}
          </Button>
        </form>

        <div className="space-y-3">
          <div className="flex items-center">
            <div className="flex-1 border-t border-white/20"></div>
            <span className="px-4 text-white/60 text-sm">or</span>
            <div className="flex-1 border-t border-white/20"></div>
          </div>
          
          <Button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="w-full glass hover:bg-white/15 text-white font-medium"
            variant="outline"
          >
            <i className="fab fa-google mr-2"></i>
            Continue with Google
          </Button>
        </div>

        <div className="text-center mt-6">
          <p className="text-white/60 text-sm">
            {isSignUp ? "Already have an account?" : "Don't have an account?"}{" "}
            <button
              type="button"
              onClick={() => setIsSignUp(!isSignUp)}
              className="text-white hover:text-white/80 smooth-transition underline"
            >
              {isSignUp ? "Sign in" : "Sign up"}
            </button>
          </p>
        </div>
      </div>
    </div>
  );
}
