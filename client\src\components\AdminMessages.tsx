import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { collection, onSnapshot, query, orderBy, addDoc, serverTimestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

interface AdminMessage {
  id: string;
  message: string;
  sender: {
    uid: string;
    displayName: string;
    email: string;
  };
  timestamp: any;
  recipients: string[];
  priority: "low" | "medium" | "high";
  type: "announcement" | "alert" | "info";
}

interface TeamMember {
  id: string;
  displayName: string;
  email: string;
  isOnline: boolean;
}

export default function AdminMessages() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [messages, setMessages] = useState<AdminMessage[]>([]);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [priority, setPriority] = useState<"low" | "medium" | "high">("medium");
  const [messageType, setMessageType] = useState<"announcement" | "alert" | "info">("announcement");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  // Load messages
  useEffect(() => {
    console.log('AdminMessages: Starting to load messages');

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('AdminMessages: Loading timeout reached, using empty array');
      setMessages([]);
      setLoading(false);
    }, 5000);

    try {
      const messagesQuery = query(
        collection(db, "adminMessages"),
        orderBy("timestamp", "desc")
      );

      const unsubscribe = onSnapshot(
        messagesQuery,
        (snapshot) => {
          console.log('AdminMessages: Received data from Firestore');
          clearTimeout(loadingTimeout);
          const messagesList: AdminMessage[] = [];
          snapshot.forEach((doc) => {
            messagesList.push({ id: doc.id, ...doc.data() } as AdminMessage);
          });
          setMessages(messagesList);
          setLoading(false);
        },
        (error) => {
          console.error('AdminMessages: Firestore error:', error);
          clearTimeout(loadingTimeout);
          setMessages([]);
          setLoading(false);
        }
      );

      return () => {
        clearTimeout(loadingTimeout);
        unsubscribe();
      };
    } catch (error) {
      console.error('AdminMessages: Setup error:', error);
      clearTimeout(loadingTimeout);
      setMessages([]);
      setLoading(false);
    }
  }, []);

  // Load team members
  useEffect(() => {
    console.log('AdminMessages: Starting to load team members');

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('AdminMessages: Members loading timeout reached, using empty array');
      setMembers([]);
    }, 5000);

    try {
      const membersQuery = query(
        collection(db, "users"),
        orderBy("displayName")
      );

      const unsubscribe = onSnapshot(
        membersQuery,
        (snapshot) => {
          console.log('AdminMessages: Received members data from Firestore');
          clearTimeout(loadingTimeout);
          const membersList: TeamMember[] = [];
          snapshot.forEach((doc) => {
            const data = doc.data();
            membersList.push({
              id: doc.id,
              displayName: data.displayName || "Unknown User",
              email: data.email || "",
              isOnline: data.isOnline || false
            });
          });
          setMembers(membersList);
        },
        (error) => {
          console.error('AdminMessages: Members Firestore error:', error);
          clearTimeout(loadingTimeout);
          setMembers([]);
        }
      );

      return () => {
        clearTimeout(loadingTimeout);
        unsubscribe();
      };
    } catch (error) {
      console.error('AdminMessages: Members setup error:', error);
      clearTimeout(loadingTimeout);
      setMembers([]);
    }
  }, []);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user) return;

    setSending(true);
    try {
      await addDoc(collection(db, "adminMessages"), {
        message: newMessage.trim(),
        sender: {
          uid: user.uid,
          displayName: user.displayName || "Admin",
          email: user.email || ""
        },
        timestamp: serverTimestamp(),
        recipients: selectedMembers.length > 0 ? selectedMembers : members.map(m => m.id),
        priority,
        type: messageType
      });

      setNewMessage("");
      setSelectedMembers([]);
      toast({
        title: "Message Sent",
        description: `${messageType.charAt(0).toUpperCase() + messageType.slice(1)} sent to ${selectedMembers.length > 0 ? selectedMembers.length : members.length} member(s)`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    } finally {
      setSending(false);
    }
  };

  const toggleMemberSelection = (memberId: string) => {
    setSelectedMembers(prev =>
      prev.includes(memberId)
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  const selectAllMembers = () => {
    setSelectedMembers(members.map(m => m.id));
  };

  const clearSelection = () => {
    setSelectedMembers([]);
  };

  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return "";
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-500/20 text-red-300 border-red-500/50";
      case "medium":
        return "bg-yellow-500/20 text-yellow-300 border-yellow-500/50";
      case "low":
        return "bg-green-500/20 text-green-300 border-green-500/50";
      default:
        return "bg-gray-500/20 text-gray-300 border-gray-500/50";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "announcement":
        return "fas fa-bullhorn";
      case "alert":
        return "fas fa-exclamation-triangle";
      case "info":
        return "fas fa-info-circle";
      default:
        return "fas fa-message";
    }
  };

  if (loading) {
    return (
      <div className="glass rounded-3xl p-6 slide-up">
        <h3 className="text-xl font-bold text-white mb-4">Admin Messages</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-20 bg-white/10 rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="glass rounded-3xl p-6 slide-up">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-white flex items-center">
          <i className="fas fa-bullhorn mr-2 text-orange-400 pulse-glow"></i>
          Admin Messages
        </h3>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
          <span className="text-white/60 text-sm">{members.length} members</span>
        </div>
      </div>

      {/* Send Message Form */}
      <div className="glass-dark rounded-xl p-4 mb-6">
        <div className="space-y-4">
          {/* Message Input */}
          <div>
            <Input
              placeholder="Type your message to the team..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              className="glass border-white/20 text-white placeholder-white/60 focus:ring-orange-500/30"
            />
          </div>

          {/* Message Options */}
          <div className="flex flex-wrap gap-2">
            <select
              value={messageType}
              onChange={(e) => setMessageType(e.target.value as any)}
              className="glass border-white/20 text-white bg-transparent rounded-lg px-3 py-2 text-sm"
            >
              <option value="announcement" className="bg-gray-800">📢 Announcement</option>
              <option value="alert" className="bg-gray-800">⚠️ Alert</option>
              <option value="info" className="bg-gray-800">ℹ️ Info</option>
            </select>

            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value as any)}
              className="glass border-white/20 text-white bg-transparent rounded-lg px-3 py-2 text-sm"
            >
              <option value="low" className="bg-gray-800">🟢 Low Priority</option>
              <option value="medium" className="bg-gray-800">🟡 Medium Priority</option>
              <option value="high" className="bg-gray-800">🔴 High Priority</option>
            </select>
          </div>

          {/* Member Selection */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-white/80 text-sm">Send to:</span>
              <div className="flex space-x-2">
                <button
                  onClick={selectAllMembers}
                  className="text-blue-400 text-xs hover:text-blue-300"
                >
                  Select All
                </button>
                <button
                  onClick={clearSelection}
                  className="text-red-400 text-xs hover:text-red-300"
                >
                  Clear
                </button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              {members.map((member) => (
                <label
                  key={member.id}
                  className="flex items-center space-x-2 glass rounded-lg p-2 cursor-pointer hover:bg-white/10"
                >
                  <input
                    type="checkbox"
                    checked={selectedMembers.includes(member.id)}
                    onChange={() => toggleMemberSelection(member.id)}
                    className="rounded border-white/20"
                  />
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <div className={`w-2 h-2 rounded-full ${member.isOnline ? 'bg-green-400' : 'bg-gray-500'}`}></div>
                    <span className="text-white text-sm truncate">{member.displayName}</span>
                  </div>
                </label>
              ))}
            </div>
            <div className="text-white/60 text-xs mt-2">
              {selectedMembers.length > 0
                ? `${selectedMembers.length} member(s) selected`
                : `All ${members.length} members will receive this message`
              }
            </div>
          </div>

          {/* Send Button */}
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white touch-feedback"
          >
            {sending ? (
              <div className="flex items-center">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                Sending...
              </div>
            ) : (
              <>
                <i className="fas fa-paper-plane mr-2"></i>
                Send Message
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Recent Messages */}
      <div className="space-y-3">
        <h4 className="text-white/80 font-medium">Recent Messages</h4>
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <i className="fas fa-inbox text-4xl text-white/20 mb-4"></i>
            <p className="text-white/60">No messages sent yet</p>
            <p className="text-white/40 text-sm">Send your first team message above</p>
          </div>
        ) : (
          messages.slice(0, 5).map((message) => (
            <div
              key={message.id}
              className={`glass-dark rounded-xl p-4 border ${getPriorityColor(message.priority)}`}
            >
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 glass rounded-lg flex items-center justify-center">
                  <i className={`${getTypeIcon(message.type)} text-sm`}></i>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-white font-medium text-sm">
                      {message.sender.displayName}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(message.priority)}`}>
                      {message.priority}
                    </span>
                  </div>
                  <p className="text-white/80 text-sm mb-2">{message.message}</p>
                  <div className="flex items-center space-x-4 text-xs text-white/50">
                    <span>{formatTimestamp(message.timestamp)}</span>
                    <span>{message.recipients.length} recipient(s)</span>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
