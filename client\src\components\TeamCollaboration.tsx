import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

interface TeamCollaborationProps {
  onClose: () => void;
}

export default function TeamCollaboration({ onClose }: TeamCollaborationProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("chat");
  const [newMessage, setNewMessage] = useState("");

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    toast({
      title: "Message Sent",
      description: "Your message has been sent to the team chat.",
    });
    setNewMessage("");
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="glass rounded-3xl p-8 w-full max-w-5xl h-[85vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <i className="fas fa-users mr-3 text-purple-400"></i>Team Collaboration
          </h2>
          <Button
            onClick={onClose}
            variant="ghost"
            className="text-white/60 hover:text-white p-2"
          >
            <i className="fas fa-times text-xl"></i>
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 glass-dark rounded-xl p-1">
          <Button
            onClick={() => setActiveTab("chat")}
            className={`flex-1 ${
              activeTab === "chat"
                ? "bg-white/20 text-white"
                : "glass hover:bg-white/10 text-white/80"
            }`}
            variant="ghost"
          >
            <i className="fas fa-comments mr-2"></i>
            Team Chat
          </Button>
          <Button
            onClick={() => setActiveTab("files")}
            className={`flex-1 ${
              activeTab === "files"
                ? "bg-white/20 text-white"
                : "glass hover:bg-white/10 text-white/80"
            }`}
            variant="ghost"
          >
            <i className="fas fa-folder mr-2"></i>
            File Sharing
          </Button>
          <Button
            onClick={() => setActiveTab("video")}
            className={`flex-1 ${
              activeTab === "video"
                ? "bg-white/20 text-white"
                : "glass hover:bg-white/10 text-white/80"
            }`}
            variant="ghost"
          >
            <i className="fas fa-video mr-2"></i>
            Video Meeting
          </Button>
        </div>

        <div className="flex-1 overflow-hidden">
          {/* Team Chat Tab */}
          {activeTab === "chat" && (
            <div className="h-full flex flex-col">
              <div className="flex-1 glass-dark rounded-2xl p-4 mb-4 overflow-y-auto">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-semibold">JD</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white font-medium text-sm">John Doe</span>
                        <span className="text-white/50 text-xs">2 minutes ago</span>
                      </div>
                      <div className="glass rounded-xl p-3">
                        <p className="text-white/90 text-sm">Hey team! I've updated the security dashboard with the latest threat detection algorithms. Ready for review!</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-semibold">SA</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white font-medium text-sm">Sarah Anderson</span>
                        <span className="text-white/50 text-xs">5 minutes ago</span>
                      </div>
                      <div className="glass rounded-xl p-3">
                        <p className="text-white/90 text-sm">Excellent work! The new AI automation features are working perfectly. Performance improved by 40%!</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-semibold">MJ</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white font-medium text-sm">Mike Johnson</span>
                        <span className="text-white/50 text-xs">10 minutes ago</span>
                      </div>
                      <div className="glass rounded-xl p-3">
                        <p className="text-white/90 text-sm">Mobile app beta testing is complete. Ready to push to production! 🚀</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 glass border-white/20 text-white placeholder-white/60"
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                />
                <Button
                  onClick={handleSendMessage}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-80 text-white"
                >
                  <i className="fas fa-paper-plane"></i>
                </Button>
              </div>
            </div>
          )}

          {/* File Sharing Tab */}
          {activeTab === "files" && (
            <div className="h-full">
              <div className="glass-dark rounded-2xl p-6 h-full">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  <div className="glass rounded-xl p-4 hover-glass smooth-transition cursor-pointer">
                    <div className="flex items-center space-x-3 mb-3">
                      <i className="fas fa-file-code text-blue-400 text-2xl"></i>
                      <div>
                        <h4 className="text-white font-medium">security-dashboard.zip</h4>
                        <p className="text-white/60 text-sm">2.3 MB • 1 hour ago</p>
                      </div>
                    </div>
                    <p className="text-white/70 text-xs">Latest security dashboard source code</p>
                  </div>

                  <div className="glass rounded-xl p-4 hover-glass smooth-transition cursor-pointer">
                    <div className="flex items-center space-x-3 mb-3">
                      <i className="fas fa-file-pdf text-red-400 text-2xl"></i>
                      <div>
                        <h4 className="text-white font-medium">project-requirements.pdf</h4>
                        <p className="text-white/60 text-sm">890 KB • 3 hours ago</p>
                      </div>
                    </div>
                    <p className="text-white/70 text-xs">Updated project specifications</p>
                  </div>

                  <div className="glass rounded-xl p-4 hover-glass smooth-transition cursor-pointer">
                    <div className="flex items-center space-x-3 mb-3">
                      <i className="fas fa-file-image text-green-400 text-2xl"></i>
                      <div>
                        <h4 className="text-white font-medium">ui-mockups.png</h4>
                        <p className="text-white/60 text-sm">1.8 MB • 5 hours ago</p>
                      </div>
                    </div>
                    <p className="text-white/70 text-xs">Mobile app interface designs</p>
                  </div>
                </div>

                <div className="border-2 border-dashed border-white/20 rounded-xl p-8 text-center">
                  <i className="fas fa-cloud-upload-alt text-4xl text-white/40 mb-4"></i>
                  <p className="text-white/60 mb-4">Drag & drop files here or click to upload</p>
                  <Button className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:opacity-80 text-white">
                    <i className="fas fa-upload mr-2"></i>
                    Choose Files
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Video Meeting Tab */}
          {activeTab === "video" && (
            <div className="h-full">
              <div className="glass-dark rounded-2xl p-6 h-full">
                <div className="text-center mb-8">
                  <div className="w-32 h-32 glass rounded-full flex items-center justify-center mx-auto mb-6">
                    <i className="fas fa-video text-4xl text-purple-400"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-2">Start Video Meeting</h3>
                  <p className="text-white/60 mb-6">Connect with your team in real-time</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="glass rounded-xl p-6 text-center">
                    <i className="fas fa-users text-3xl text-blue-400 mb-4"></i>
                    <h4 className="text-white font-semibold mb-2">Team Meeting</h4>
                    <p className="text-white/60 text-sm mb-4">Start a meeting with all team members</p>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                      <i className="fas fa-video mr-2"></i>
                      Start Team Call
                    </Button>
                  </div>

                  <div className="glass rounded-xl p-6 text-center">
                    <i className="fas fa-user text-3xl text-green-400 mb-4"></i>
                    <h4 className="text-white font-semibold mb-2">One-on-One</h4>
                    <p className="text-white/60 text-sm mb-4">Private meeting with a team member</p>
                    <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                      <i className="fas fa-phone mr-2"></i>
                      Start Private Call
                    </Button>
                  </div>
                </div>

                <div className="glass rounded-xl p-4">
                  <h4 className="text-white font-medium mb-3">Recent Meetings</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <i className="fas fa-video text-purple-400"></i>
                        <div>
                          <p className="text-white text-sm">Daily Standup</p>
                          <p className="text-white/60 text-xs">Yesterday, 9:00 AM</p>
                        </div>
                      </div>
                      <Button size="sm" variant="ghost" className="text-white/60 hover:text-white">
                        <i className="fas fa-play"></i>
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <i className="fas fa-video text-purple-400"></i>
                        <div>
                          <p className="text-white text-sm">Project Review</p>
                          <p className="text-white/60 text-xs">Monday, 2:00 PM</p>
                        </div>
                      </div>
                      <Button size="sm" variant="ghost" className="text-white/60 hover:text-white">
                        <i className="fas fa-play"></i>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}