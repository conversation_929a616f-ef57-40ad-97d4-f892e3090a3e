import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useMessages } from "@/hooks/useFirestore";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import AdminTaskManager from "./AdminTaskManager";

interface AdminPanelProps {
  onClose: () => void;
}

export default function AdminPanel({ onClose }: AdminPanelProps) {
  const { user } = useAuth();
  const { addMessage } = useMessages();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("broadcast");
  const [showTaskManager, setShowTaskManager] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    isUrgent: false,
    pushNotification: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !formData.content.trim()) return;

    try {
      setLoading(true);
      await addMessage({
        adminId: user.uid,
        title: formData.title,
        content: formData.content,
        isUrgent: formData.isUrgent,
        pushNotification: formData.pushNotification,
      });

      toast({
        title: "Message Sent",
        description: "Your message has been broadcast to all team members",
      });

      setFormData({
        title: "",
        content: "",
        isUrgent: false,
        pushNotification: false,
      });
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Mock users for display
  const mockUsers = [
    { id: "1", name: "John Doe", email: "<EMAIL>", initials: "JD", online: true },
    { id: "2", name: "Sarah Anderson", email: "<EMAIL>", initials: "SA", online: false },
    { id: "3", name: "Mike Johnson", email: "<EMAIL>", initials: "MJ", online: true },
  ];

  if (showTaskManager) {
    return <AdminTaskManager onClose={() => setShowTaskManager(false)} />;
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="glass rounded-3xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <i className="fas fa-crown mr-3 text-yellow-400"></i>Admin Control Center
          </h2>
          <Button
            onClick={onClose}
            variant="ghost"
            className="text-white/60 hover:text-white p-2"
          >
            <i className="fas fa-times text-xl"></i>
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 glass-dark rounded-xl p-1">
          <Button
            onClick={() => setActiveTab("broadcast")}
            className={`flex-1 ${
              activeTab === "broadcast"
                ? "bg-white/20 text-white"
                : "glass hover:bg-white/10 text-white/80"
            }`}
            variant="ghost"
          >
            <i className="fas fa-broadcast-tower mr-2"></i>
            Broadcast
          </Button>
          <Button
            onClick={() => setActiveTab("tasks")}
            className={`flex-1 ${
              activeTab === "tasks"
                ? "bg-white/20 text-white"
                : "glass hover:bg-white/10 text-white/80"
            }`}
            variant="ghost"
          >
            <i className="fas fa-tasks mr-2"></i>
            Task Manager
          </Button>
          <Button
            onClick={() => setActiveTab("analytics")}
            className={`flex-1 ${
              activeTab === "analytics"
                ? "bg-white/20 text-white"
                : "glass hover:bg-white/10 text-white/80"
            }`}
            variant="ghost"
          >
            <i className="fas fa-chart-bar mr-2"></i>
            Analytics
          </Button>
        </div>

        <div className="space-y-6">
          {/* Broadcast Tab */}
          {activeTab === "broadcast" && (
            <>
              <div className="glass-dark rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Broadcast Message</h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-white/80 text-sm mb-2">Message Title</label>
                    <Input
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      placeholder="Enter message title..."
                      className="glass border-white/20 text-white placeholder-white/60"
                    />
                  </div>
                  <div>
                    <label className="block text-white/80 text-sm mb-2">Message Content</label>
                    <Textarea
                      value={formData.content}
                      onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                      rows={4}
                      placeholder="Type your message to all team members..."
                      className="glass border-white/20 text-white placeholder-white/60 resize-none"
                      required
                    />
                  </div>
                  <div className="flex items-center space-x-6">
                    <label className="flex items-center space-x-2">
                      <Checkbox
                        checked={formData.isUrgent}
                        onCheckedChange={(checked) =>
                          setFormData({ ...formData, isUrgent: !!checked })
                        }
                        className="border-white/30 data-[state=checked]:bg-red-500"
                      />
                      <span className="text-white/80 text-sm">Mark as urgent</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <Checkbox
                        checked={formData.pushNotification}
                        onCheckedChange={(checked) =>
                          setFormData({ ...formData, pushNotification: !!checked })
                        }
                        className="border-white/30 data-[state=checked]:bg-[hsl(var(--primary))]"
                      />
                      <span className="text-white/80 text-sm">Send push notification</span>
                    </label>
                  </div>
                  <Button
                    type="submit"
                    disabled={loading || !formData.content.trim()}
                    className="w-full bg-gradient-to-r from-[hsl(var(--primary))] to-[hsl(var(--secondary))] hover:opacity-80 text-white font-semibold"
                  >
                    <i className="fas fa-broadcast-tower mr-2"></i>
                    {loading ? "Sending..." : "Send to All Members"}
                  </Button>
                </form>
              </div>

              <div className="glass-dark rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Team Members</h3>
                <div className="space-y-3">
                  {mockUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-3 glass rounded-xl">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-[hsl(var(--primary))] to-[hsl(var(--secondary))] rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold text-sm">{user.initials}</span>
                        </div>
                        <div>
                          <p className="text-white font-medium">{user.name}</p>
                          <p className="text-white/60 text-sm">{user.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span
                          className={`w-3 h-3 rounded-full ${
                            user.online ? "bg-green-400" : "bg-gray-400"
                          }`}
                        ></span>
                        <span
                          className={`text-sm ${
                            user.online ? "text-green-400" : "text-gray-400"
                          }`}
                        >
                          {user.online ? "Online" : "Offline"}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Task Manager Tab */}
          {activeTab === "tasks" && (
            <div className="glass-dark rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Task Management Controls</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="glass rounded-xl p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                      <i className="fas fa-edit text-white text-lg"></i>
                    </div>
                    <div>
                      <h4 className="text-white font-medium">Modify Tasks</h4>
                      <p className="text-white/60 text-sm">Edit any team member's tasks</p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setShowTaskManager(true)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <i className="fas fa-external-link-alt mr-2"></i>
                    Open Task Manager
                  </Button>
                </div>

                <div className="glass rounded-xl p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-12 h-12 bg-red-600 rounded-xl flex items-center justify-center">
                      <i className="fas fa-trash text-white text-lg"></i>
                    </div>
                    <div>
                      <h4 className="text-white font-medium">Delete Tasks</h4>
                      <p className="text-white/60 text-sm">Remove tasks permanently</p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setShowTaskManager(true)}
                    className="w-full bg-red-600 hover:bg-red-700 text-white"
                  >
                    <i className="fas fa-external-link-alt mr-2"></i>
                    Manage Deletions
                  </Button>
                </div>
              </div>

              <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                  <span className="text-yellow-300 font-medium">Admin Privileges</span>
                </div>
                <p className="text-yellow-200/80 text-sm">
                  As an admin, you have complete control over all team tasks. All modifications and deletions are logged for audit purposes.
                </p>
              </div>
            </div>
          )}

          {/* Analytics Tab */}
          {activeTab === "analytics" && (
            <div className="glass-dark rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Team Analytics</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="glass rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-green-400">89%</div>
                  <div className="text-white/60 text-sm">Completion Rate</div>
                </div>
                <div className="glass rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">24</div>
                  <div className="text-white/60 text-sm">Active Tasks</div>
                </div>
                <div className="glass rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-400">3</div>
                  <div className="text-white/60 text-sm">Team Members</div>
                </div>
                <div className="glass rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400">12</div>
                  <div className="text-white/60 text-sm">Messages Sent</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="glass rounded-xl p-4">
                  <h4 className="text-white font-medium mb-3">Recent Admin Actions</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/80">Task deleted: "Code Review"</span>
                      <span className="text-white/50">2 min ago</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/80">Message broadcast sent</span>
                      <span className="text-white/50">1 hour ago</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/80">Task modified: "Bug Fix"</span>
                      <span className="text-white/50">3 hours ago</span>
                    </div>
                  </div>
                </div>

                <div className="glass rounded-xl p-4">
                  <h4 className="text-white font-medium mb-3">Priority Distribution</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-red-300">High Priority</span>
                      <span className="text-white">8 tasks</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-blue-300">Medium Priority</span>
                      <span className="text-white">12 tasks</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-green-300">Low Priority</span>
                      <span className="text-white">4 tasks</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
