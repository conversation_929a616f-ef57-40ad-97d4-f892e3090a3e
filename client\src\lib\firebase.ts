import { initializeApp, getApps } from "firebase/app";
import {
  getAuth,
  signInWithRedirect,
  signInWithPopup,
  GoogleAuthProvider,
  getRedirectResult,
  signOut,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  updateProfile,
  setPersistence,
  browserLocalPersistence
} from "firebase/auth";
import {
  getFirestore,
  doc,
  setDoc,
  connectFirestoreEmulator,
  enableNetwork,
  disableNetwork,
  CACHE_SIZE_UNLIMITED,
  initializeFirestore
} from "firebase/firestore";
import { getDatabase, ref, push, set, onValue, off } from "firebase/database";
import { getAnalytics } from "firebase/analytics";

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyAPAEOXHFg1MFjCdstL38tKZU0tstGbxJs",
  authDomain: (import.meta.env.VITE_FIREBASE_PROJECT_ID || "to-do-cyber") + ".firebaseapp.com",
  databaseURL: "https://to-do-cyber-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "to-do-cyber",
  storageBucket: (import.meta.env.VITE_FIREBASE_PROJECT_ID || "to-do-cyber") + ".firebasestorage.app",
  messagingSenderId: "262386981693",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:262386981693:web:a128fe38e8be49cddd8354",
  measurementId: "G-C603FSNN8H"
};

// Initialize Firebase only if it hasn't been initialized already
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
export const auth = getAuth(app);

// Set auth persistence to local storage
if (typeof window !== 'undefined') {
  setPersistence(auth, browserLocalPersistence).catch((error) => {
    console.error('Error setting auth persistence:', error);
  });
}

// Initialize Firestore with better settings - Force long polling to avoid WebChannel issues
export const db = (() => {
  try {
    return initializeFirestore(app, {
      cacheSizeBytes: CACHE_SIZE_UNLIMITED,
      experimentalForceLongPolling: true, // Force long polling to avoid WebChannel connection issues
      ignoreUndefinedProperties: true,
      experimentalAutoDetectLongPolling: false // Disable auto-detection to force long polling
    });
  } catch (error: any) {
    // If already initialized, get the existing instance
    console.log("Firestore already initialized, using existing instance:", error.message);
    try {
      return getFirestore(app);
    } catch (fallbackError) {
      console.error("Failed to get Firestore instance:", fallbackError);
      // Return null to prevent crashes - components will handle this gracefully
      return null as any;
    }
  }
})();

export const rtdb = getDatabase(app);

// Add connection monitoring
let isOnline = true;
export const getConnectionStatus = () => isOnline;

// Monitor network status and handle Firestore accordingly
if (typeof window !== 'undefined') {
  window.addEventListener('online', async () => {
    console.log('Network back online, enabling Firestore');
    isOnline = true;
    try {
      await enableNetwork(db);
    } catch (error) {
      console.log('Error enabling Firestore network:', error);
    }
  });

  window.addEventListener('offline', async () => {
    console.log('Network offline, disabling Firestore');
    isOnline = false;
    try {
      await disableNetwork(db);
    } catch (error) {
      console.log('Error disabling Firestore network:', error);
    }
  });
}

// Initialize analytics only in browser environment
let analytics;
if (typeof window !== 'undefined') {
  analytics = getAnalytics(app);
}
export { analytics };

const provider = new GoogleAuthProvider();

export const signInWithGoogle = () => {
  return signInWithPopup(auth, provider);
};

export const handleRedirect = async () => {
  try {
    const result = await getRedirectResult(auth);
    return result;
  } catch (error) {
    console.error("Error handling redirect:", error);
    throw error;
  }
};

export const signUpWithEmail = async (email: string, password: string, displayName: string) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Check if this is the admin user
    const isAdminUser = email === "<EMAIL>";

    // Update the user's display name
    await updateProfile(user, {
      displayName: displayName
    });

    // Create user document in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      uid: user.uid,
      email: user.email,
      displayName: displayName,
      photoURL: user.photoURL,
      createdAt: new Date(),
      isAdmin: isAdminUser
    });

    // Also store in Realtime Database for faster access
    await set(ref(rtdb, `users/${user.uid}`), {
      uid: user.uid,
      email: user.email,
      displayName: displayName,
      photoURL: user.photoURL,
      isAdmin: isAdminUser,
      lastSeen: new Date().toISOString()
    });

    return userCredential;
  } catch (error) {
    console.error("Error signing up:", error);
    throw error;
  }
};

export const signInWithEmail = async (email: string, password: string) => {
  return signInWithEmailAndPassword(auth, email, password);
};

export const signOutUser = () => {
  return signOut(auth);
};

// Utility function to handle Firestore operations with retry logic
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      console.log(`Operation failed (attempt ${i + 1}/${maxRetries}):`, error.message);

      // Don't retry on certain errors
      if (error.code === 'permission-denied' || error.code === 'unauthenticated') {
        throw error;
      }

      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
  }

  throw lastError!;
};

// Enhanced error handling for Firestore operations
export const handleFirestoreError = (error: any) => {
  console.error('Firestore error:', error);

  switch (error.code) {
    case 'unavailable':
      return 'Service temporarily unavailable. Please try again.';
    case 'permission-denied':
      return 'You do not have permission to perform this action.';
    case 'unauthenticated':
      return 'Please sign in to continue.';
    case 'failed-precondition':
      return 'Operation failed. Please refresh and try again.';
    case 'aborted':
      return 'Operation was aborted. Please try again.';
    case 'deadline-exceeded':
      return 'Request timed out. Please check your connection.';
    default:
      return 'An unexpected error occurred. Please try again.';
  }
};

// Firebase health check
export const checkFirebaseHealth = async (): Promise<boolean> => {
  try {
    // Check if Firebase is properly initialized
    if (!app || !auth) {
      console.error('Firebase app or auth not initialized');
      return false;
    }

    // Check if Firestore is accessible (but don't fail if it's not)
    if (db) {
      console.log('Firestore instance available');
    } else {
      console.warn('Firestore instance not available, but continuing');
    }

    console.log('Firebase health check passed');
    return true;
  } catch (error) {
    console.error('Firebase health check failed:', error);
    return false;
  }
};
