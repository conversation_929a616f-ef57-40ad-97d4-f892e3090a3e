import { useAuth } from "@/hooks/useAuth";
import { useState, useEffect } from "react";
import { checkFirebaseHealth } from "@/lib/firebase";
import AuthScreen from "@/components/AuthScreen";
import MainApp from "@/components/MainApp";

export default function Home() {
  const { user, loading } = useAuth();
  const [showFallback, setShowFallback] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState("Initializing...");
  const [firebaseHealthy, setFirebaseHealthy] = useState<boolean | null>(null);
  const [debugMode, setDebugMode] = useState(false);

  useEffect(() => {
    if (!loading) return;

    console.log('Home: Loading state is true, starting timers...');

    // Check Firebase health
    checkFirebaseHealth().then((healthy) => {
      console.log('Firebase health check result:', healthy);
      setFirebaseHealthy(healthy);
    });

    // Show fallback after 6 seconds of loading
    const fallbackTimer = setTimeout(() => {
      console.log('Fallback timer triggered after 6 seconds');
      setShowFallback(true);
    }, 6000);

    // Force skip loading after 10 seconds
    const forceSkipTimer = setTimeout(() => {
      console.log('Force skip timer triggered after 10 seconds - bypassing loading');
      setDebugMode(true);
    }, 10000);

    // Simulate loading progress and messages
    const progressInterval = setInterval(() => {
      setLoadingProgress(prev => {
        const newProgress = Math.min(prev + Math.random() * 15, 95);

        // Update loading messages based on progress
        if (newProgress < 20) {
          setLoadingMessage("Connecting to Firebase...");
        } else if (newProgress < 40) {
          setLoadingMessage("Authenticating user...");
        } else if (newProgress < 60) {
          setLoadingMessage("Loading workspace...");
        } else if (newProgress < 80) {
          setLoadingMessage("Preparing components...");
        } else {
          setLoadingMessage("Almost ready...");
        }

        return newProgress;
      });
    }, 300);

    return () => {
      clearTimeout(fallbackTimer);
      clearTimeout(forceSkipTimer);
      clearInterval(progressInterval);
    };
  }, [loading]);

  // Debug mode bypass
  if (debugMode) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="glass rounded-3xl p-8 bounce-in max-w-md w-full mx-4">
          <div className="text-center space-y-6">
            <h1 className="text-2xl font-bold text-white">Debug Mode</h1>
            <p className="text-white/70">Loading took too long. Choose an option:</p>
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg transition-all duration-200 touch-feedback"
              >
                🔄 Reload Page
              </button>
              <button
                onClick={() => {
                  console.log('Forcing to AuthScreen');
                  setDebugMode(false);
                }}
                className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg transition-all duration-200 touch-feedback"
              >
                🚀 Continue to App
              </button>
              <button
                onClick={() => {
                  localStorage.clear();
                  window.location.reload();
                }}
                className="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg transition-all duration-200 touch-feedback"
              >
                🗑️ Clear Cache & Reload
              </button>
            </div>
            <div className="text-xs text-white/50 space-y-1">
              <p>User: {user ? 'Logged in' : 'Not logged in'}</p>
              <p>Loading: {loading ? 'True' : 'False'}</p>
              <p>Firebase: {firebaseHealthy ? 'Healthy' : 'Issues'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="glass rounded-3xl p-8 bounce-in max-w-md w-full mx-4">
          <div className="text-center space-y-6">
            {/* Logo/Title */}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-white">TaskMaster Pro</h1>
              <p className="text-white/60">Initializing your workspace...</p>
            </div>

            {/* Loading Spinner */}
            <div className="flex justify-center">
              <div className="w-12 h-12 border-4 border-white/20 border-t-white rounded-full animate-spin pulse-glow"></div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="w-full bg-white/10 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${loadingProgress}%` }}
                ></div>
              </div>
              <p className="text-white/50 text-sm">{Math.round(loadingProgress)}% loaded</p>
            </div>

            {/* Loading Messages */}
            <div className="text-white/70 text-sm min-h-[20px]">
              {loadingMessage}
            </div>

            {/* Firebase Health Status */}
            {firebaseHealthy !== null && (
              <div className="flex items-center justify-center space-x-2 text-sm">
                <div className={`w-2 h-2 rounded-full ${firebaseHealthy ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-white/60">
                  Firebase: {firebaseHealthy ? 'Connected' : 'Connection Issues'}
                </span>
              </div>
            )}

            {/* Fallback Option */}
            {showFallback && (
              <div className="space-y-4 pt-4 border-t border-white/10">
                <p className="text-white/60 text-sm">Taking longer than expected?</p>
                <div className="space-y-2">
                  <button
                    onClick={() => window.location.reload()}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-all duration-200 touch-feedback"
                  >
                    Refresh Page
                  </button>
                  <button
                    onClick={() => {
                      // Force skip loading for offline mode
                      console.log("Forcing offline mode");
                      window.location.hash = "#offline";
                      window.location.reload();
                    }}
                    className="w-full glass hover:bg-white/10 text-white py-2 px-4 rounded-lg transition-all duration-200 touch-feedback"
                  >
                    Continue Offline
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return user ? <MainApp /> : <AuthScreen />;
}
