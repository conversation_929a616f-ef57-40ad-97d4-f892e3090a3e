import { useAuth } from "@/hooks/useAuth";
import AuthScreen from "@/components/AuthScreen";
import MainApp from "@/components/MainApp";

export default function Home() {
  const { user, loading } = useAuth();

  console.log('Home: Rendering with user:', user ? 'logged in' : 'not logged in', 'loading:', loading);

  // Skip all loading states and go directly to auth/main app
  return user ? <MainApp /> : <AuthScreen />;
}
