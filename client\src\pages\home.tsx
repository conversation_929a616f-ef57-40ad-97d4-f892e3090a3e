import { useAuth } from "@/hooks/useAuth";
import AuthScreen from "@/components/AuthScreen";
import MainApp from "@/components/MainApp";

export default function Home() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen gradient-bg-alt flex items-center justify-center">
        <div className="glass rounded-3xl p-8">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 border-4 border-white/20 border-t-white rounded-full animate-spin"></div>
            <span className="text-white text-lg">Loading Cyber Community Task...</span>
          </div>
        </div>
      </div>
    );
  }

  return user ? <MainApp /> : <AuthScreen />;
}
