import { useState, useEffect } from "react";
import { rtdb } from "@/lib/firebase";
import { ref, push, set, onValue, off, serverTimestamp } from "firebase/database";
import { useAuth } from "./useAuth";

export function useRealtimeTasks() {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setTasks([]);
      setLoading(false);
      return;
    }

    const tasksRef = ref(rtdb, 'tasks');
    const unsubscribe = onValue(tasksRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const userTasks = Object.entries(data)
          .filter(([_, task]: [string, any]) => task.userId === user.uid)
          .map(([id, task]) => ({ id, ...(task as any) }))
          .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        setTasks(userTasks);
      } else {
        setTasks([]);
      }
      setLoading(false);
    });

    return () => off(tasksRef, 'value', unsubscribe);
  }, [user]);

  const addTask = async (taskData: any) => {
    if (!user) return;

    const tasksRef = ref(rtdb, 'tasks');
    const newTaskRef = push(tasksRef);

    await set(newTaskRef, {
      ...taskData,
      userId: user.uid,
      status: "active",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  };

  const updateTask = async (id: string, updates: any) => {
    const taskRef = ref(rtdb, `tasks/${id}`);
    await set(taskRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  };

  const deleteTask = async (id: string) => {
    const taskRef = ref(rtdb, `tasks/${id}`);
    await set(taskRef, null);
  };

  return { tasks, loading, addTask, updateTask, deleteTask };
}

export function useRealtimeMessages() {
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const messagesRef = ref(rtdb, 'messages');
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messagesList = Object.entries(data)
          .map(([id, message]) => ({ id, ...(message as any) }))
          .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        setMessages(messagesList);
      } else {
        setMessages([]);
      }
      setLoading(false);
    });

    return () => off(messagesRef, 'value', unsubscribe);
  }, []);

  const addMessage = async (messageData: any) => {
    const messagesRef = ref(rtdb, 'messages');
    const newMessageRef = push(messagesRef);

    await set(newMessageRef, {
      ...messageData,
      createdAt: new Date().toISOString()
    });
  };

  return { messages, loading, addMessage };
}

export function useRealtimeChat() {
  const { user } = useAuth();
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const chatRef = ref(rtdb, 'chat');
    const unsubscribe = onValue(chatRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messagesList = Object.entries(data)
          .map(([id, message]) => ({ id, ...(message as any) }))
          .sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        setChatMessages(messagesList);
      } else {
        setChatMessages([]);
      }
      setLoading(false);
    });

    return () => off(chatRef, 'value', unsubscribe);
  }, []);

  const sendMessage = async (message: string) => {
    if (!user || !message.trim()) return;

    const chatRef = ref(rtdb, 'chat');
    const newMessageRef = push(chatRef);

    await set(newMessageRef, {
      message: message.trim(),
      userId: user.uid,
      displayName: user.displayName || user.email,
      timestamp: new Date().toISOString()
    });
  };

  return { chatMessages, loading, sendMessage };
}

// Alias for compatibility
export const useTasks = useRealtimeTasks;

export function useAdminRealtimeTasks() {
  const { user } = useAuth();
  const [allTasks, setAllTasks] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setAllTasks([]);
      setAllUsers([]);
      setLoading(false);
      return;
    }

    // Listen to all tasks for admin
    const tasksRef = ref(rtdb, 'tasks');
    const unsubscribeTasks = onValue(tasksRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const tasksList = Object.entries(data)
          .map(([id, task]) => ({ id, ...(task as any) }))
          .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        setAllTasks(tasksList);
      } else {
        setAllTasks([]);
      }
      setLoading(false);
    });

    // Listen to all users for admin
    const usersRef = ref(rtdb, 'users');
    const unsubscribeUsers = onValue(usersRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const usersList = Object.entries(data).map(([id, user]) => ({ id, ...(user as any) }));
        setAllUsers(usersList);
      } else {
        setAllUsers([]);
      }
    });

    return () => {
      off(tasksRef, 'value', unsubscribeTasks);
      off(usersRef, 'value', unsubscribeUsers);
    };
  }, [user]);

  const adminDeleteTask = async (taskId: string, taskDetails: any) => {
    if (!user) return;

    // Log admin action
    const adminActionsRef = ref(rtdb, 'adminActions');
    const newActionRef = push(adminActionsRef);
    await set(newActionRef, {
      adminId: user.uid,
      action: "delete_task",
      targetId: taskId,
      details: JSON.stringify(taskDetails),
      timestamp: new Date().toISOString()
    });

    // Delete the task
    const taskRef = ref(rtdb, `tasks/${taskId}`);
    await set(taskRef, null);
  };

  const adminUpdateTask = async (taskId: string, updates: any, originalTask: any) => {
    if (!user) return;

    // Log admin action
    const adminActionsRef = ref(rtdb, 'adminActions');
    const newActionRef = push(adminActionsRef);
    await set(newActionRef, {
      adminId: user.uid,
      action: "modify_task",
      targetId: taskId,
      details: JSON.stringify({ original: originalTask, updates }),
      timestamp: new Date().toISOString()
    });

    // Update the task
    const taskRef = ref(rtdb, `tasks/${taskId}`);
    await set(taskRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
      lastModifiedBy: user.uid
    });
  };

  const getUserById = (userId: string) => {
    return allUsers.find(u => u.uid === userId || u.id === userId);
  };

  return {
    allTasks,
    allUsers,
    loading,
    adminDeleteTask,
    adminUpdateTask,
    getUserById
  };
}