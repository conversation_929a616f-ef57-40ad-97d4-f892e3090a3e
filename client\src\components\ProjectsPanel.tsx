import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface ProjectsPanelProps {
  onClose: () => void;
}

export default function ProjectsPanel({ onClose }: ProjectsPanelProps) {
  const { toast } = useToast();
  const [showCreateProject, setShowCreateProject] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "development",
    deadline: "",
    priority: "medium"
  });

  // Mock projects data
  const projects = [
    {
      id: "1",
      name: "Cyber Security Dashboard",
      description: "Advanced security monitoring platform",
      category: "development",
      progress: 75,
      deadline: "2024-02-15",
      priority: "high",
      members: 8,
      tasks: 24,
      status: "active"
    },
    {
      id: "2", 
      name: "AI Task Automation",
      description: "Machine learning powered task management",
      category: "ai",
      progress: 45,
      deadline: "2024-03-01",
      priority: "medium",
      members: 5,
      tasks: 18,
      status: "active"
    },
    {
      id: "3",
      name: "Mobile App Development",
      description: "Cross-platform mobile application",
      category: "mobile",
      progress: 90,
      deadline: "2024-01-30",
      priority: "high",
      members: 6,
      tasks: 12,
      status: "review"
    }
  ];

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Project Created!",
      description: `${formData.name} project has been successfully created.`,
    });
    setShowCreateProject(false);
    setFormData({ name: "", description: "", category: "development", deadline: "", priority: "medium" });
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return "from-green-500 to-green-600";
    if (progress >= 50) return "from-blue-500 to-blue-600";
    return "from-orange-500 to-orange-600";
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "development": return "fas fa-code";
      case "ai": return "fas fa-robot";
      case "mobile": return "fas fa-mobile-alt";
      case "design": return "fas fa-palette";
      default: return "fas fa-project-diagram";
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="glass rounded-3xl p-8 w-full max-w-6xl h-[85vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <i className="fas fa-project-diagram mr-3 text-cyan-400"></i>Team Projects
          </h2>
          <div className="flex space-x-3">
            <Button
              onClick={() => setShowCreateProject(true)}
              className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:opacity-80 text-white"
            >
              <i className="fas fa-plus mr-2"></i>New Project
            </Button>
            <Button
              onClick={onClose}
              variant="ghost"
              className="text-white/60 hover:text-white p-2"
            >
              <i className="fas fa-times text-xl"></i>
            </Button>
          </div>
        </div>

        {showCreateProject ? (
          <div className="glass-dark rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Create New Project</h3>
            <form onSubmit={handleCreateProject} className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-white/80 text-sm mb-2">Project Name</label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter project name..."
                  className="glass border-white/20 text-white placeholder-white/60"
                  required
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-white/80 text-sm mb-2">Description</label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Project description..."
                  rows={3}
                  className="glass border-white/20 text-white placeholder-white/60 resize-none"
                />
              </div>
              <div>
                <label className="block text-white/80 text-sm mb-2">Category</label>
                <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                  <SelectTrigger className="glass border-white/20 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="development">Development</SelectItem>
                    <SelectItem value="ai">AI/ML</SelectItem>
                    <SelectItem value="mobile">Mobile</SelectItem>
                    <SelectItem value="design">Design</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-white/80 text-sm mb-2">Priority</label>
                <Select value={formData.priority} onValueChange={(value) => setFormData({ ...formData, priority: value })}>
                  <SelectTrigger className="glass border-white/20 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-white/80 text-sm mb-2">Deadline</label>
                <Input
                  type="date"
                  value={formData.deadline}
                  onChange={(e) => setFormData({ ...formData, deadline: e.target.value })}
                  className="glass border-white/20 text-white"
                />
              </div>
              <div className="flex space-x-3">
                <Button
                  type="button"
                  onClick={() => setShowCreateProject(false)}
                  variant="ghost"
                  className="flex-1 glass hover:bg-white/15 text-white"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 hover:opacity-80 text-white"
                >
                  Create Project
                </Button>
              </div>
            </form>
          </div>
        ) : (
          <div className="flex-1 overflow-y-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {projects.map((project) => (
                <div key={project.id} className="glass hover-glass rounded-2xl p-6 smooth-transition">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 glass rounded-xl flex items-center justify-center">
                        <i className={`${getCategoryIcon(project.category)} text-cyan-400 text-lg`}></i>
                      </div>
                      <div>
                        <h3 className="text-white font-semibold">{project.name}</h3>
                        <span className="text-white/60 text-sm capitalize">{project.category}</span>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-lg text-xs ${
                      project.status === "active" ? "bg-green-500/20 text-green-300" :
                      project.status === "review" ? "bg-yellow-500/20 text-yellow-300" :
                      "bg-gray-500/20 text-gray-300"
                    }`}>
                      {project.status.toUpperCase()}
                    </span>
                  </div>

                  <p className="text-white/70 text-sm mb-4 line-clamp-2">{project.description}</p>

                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm text-white/60 mb-2">
                      <span>Progress</span>
                      <span>{project.progress}%</span>
                    </div>
                    <div className="w-full glass rounded-full h-2">
                      <div
                        className={`bg-gradient-to-r ${getProgressColor(project.progress)} h-2 rounded-full transition-all duration-500`}
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{project.members}</div>
                      <div className="text-xs text-white/60">Members</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{project.tasks}</div>
                      <div className="text-xs text-white/60">Tasks</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-white/50 text-xs">
                      <i className="fas fa-calendar mr-1"></i>
                      Due: {new Date(project.deadline).toLocaleDateString()}
                    </span>
                    <Button
                      size="sm"
                      className="bg-cyan-600 hover:bg-cyan-700 text-white text-xs px-3 py-1"
                    >
                      <i className="fas fa-external-link-alt mr-1"></i>
                      Open
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}