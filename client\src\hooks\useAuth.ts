import { useState, useEffect } from "react";
import { User } from "firebase/auth";
import { auth, handleRedirect, db } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";
import { doc, setDoc, serverTimestamp } from "firebase/firestore";

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // Create or update user document in Firestore
        try {
          await setDoc(doc(db, "users", user.uid), {
            uid: user.uid,
            displayName: user.displayName || user.email?.split('@')[0] || "User",
            email: user.email || "",
            photoURL: user.photoURL || null,
            isOnline: true,
            lastSeen: serverTimestamp(),
            role: "Member", // Default role
            tasksCompleted: 0,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          }, { merge: true }); // Use merge to not overwrite existing data
        } catch (error) {
          console.error("Error creating/updating user document:", error);
        }
      }

      setUser(user);
      setLoading(false);
    });

    // Handle redirect result when component mounts
    handleRedirect().catch(console.error);

    return unsubscribe;
  }, []);

  return { user, loading };
}
