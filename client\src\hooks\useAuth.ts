import { useState, useEffect } from "react";
import { User } from "firebase/auth";
import { auth, handleRedirect, db, withRetry, handleFirestoreError } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";
import { doc, setDoc, serverTimestamp } from "firebase/firestore";
import { handlePotentialOfflineError } from "@/lib/offlineMode";

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set a maximum loading time of 8 seconds
    const loadingTimeout = setTimeout(() => {
      console.log('Auth loading timeout reached, setting loading to false');
      setLoading(false);
    }, 8000);

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      // Clear the timeout since we got a response
      clearTimeout(loadingTimeout);
      if (user) {
        // Create or update user document in Firestore with retry logic
        try {
          await withRetry(async () => {
            await setDoc(doc(db, "users", user.uid), {
              uid: user.uid,
              displayName: user.displayName || user.email?.split('@')[0] || "User",
              email: user.email || "",
              photoURL: user.photoURL || null,
              isOnline: true,
              lastSeen: serverTimestamp(),
              role: "Member", // Default role
              tasksCompleted: 0,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            }, { merge: true }); // Use merge to not overwrite existing data
          });
        } catch (error: any) {
          // Check if this is an offline error
          if (handlePotentialOfflineError(error)) {
            console.log("Switched to offline mode due to Firestore error");
          } else {
            const errorMessage = handleFirestoreError(error);
            console.error("Error creating/updating user document:", errorMessage);
          }
          // Don't block authentication for Firestore errors
        }
      }

      setUser(user);
      setLoading(false);
    });

    // Handle redirect result when component mounts
    handleRedirect().catch(console.error);

    return () => {
      clearTimeout(loadingTimeout);
      unsubscribe();
    };
  }, []);

  return { user, loading };
}
