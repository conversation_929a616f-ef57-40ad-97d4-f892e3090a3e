import { useState, useEffect } from "react";
import { User } from "firebase/auth";
import { auth, handleRedirect } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    // Handle redirect result when component mounts
    handleRedirect().catch(console.error);

    return unsubscribe;
  }, []);

  return { user, loading };
}
