import { useState, useEffect } from "react";
import { signOutUser } from "@/lib/firebase";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import NotificationPanel from "./NotificationPanel";
import FeatureSidebar from "./FeatureSidebar";
import SearchModal from "./SearchModal";

interface NavigationHeaderProps {
  onShowAdmin: () => void;
}

export default function NavigationHeader({ onShowAdmin }: NavigationHeaderProps) {
  const { user } = useAuth();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showFeatures, setShowFeatures] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'k':
            event.preventDefault();
            setShowSearch(true);
            break;
          case 'f':
            event.preventDefault();
            setShowFeatures(true);
            break;
          case 'n':
            event.preventDefault();
            setShowNotifications(true);
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleSignOut = async () => {
    try {
      await signOutUser();
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const getInitials = (name: string | null) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <>
      <nav className="glass-dark p-3 sm:p-4 sticky top-0 z-50 border-b-2 border-purple-500/30 backdrop-blur-xl shadow-lg shadow-purple-500/10">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="w-10 h-10 glass rounded-xl flex items-center justify-center pulse-glow">
              <i className="fas fa-rocket text-white"></i>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-white font-bold text-lg sm:text-xl">TaskMaster Pro</h1>
              <div className="text-white/50 text-xs">
                {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
            <div className="sm:hidden">
              <h1 className="text-white font-bold text-lg">TaskMaster</h1>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={() => setShowSearch(true)}
              className="text-white/80 hover:text-white smooth-transition touch-feedback flex items-center space-x-2 glass rounded-lg px-3 py-2"
              title="Search (Ctrl+K)"
            >
              <i className="fas fa-search"></i>
              <span className="hidden lg:inline">Search</span>
            </button>
            <button className="text-white/80 hover:text-white smooth-transition touch-feedback flex items-center space-x-2">
              <i className="fas fa-tachometer-alt"></i>
              <span>Dashboard</span>
            </button>
            <button
              onClick={() => setShowFeatures(true)}
              className="text-white/80 hover:text-white smooth-transition touch-feedback flex items-center space-x-2"
              title="Features (Ctrl+F)"
            >
              <i className="fas fa-magic text-purple-400"></i>
              <span>Features</span>
            </button>
            <button
              onClick={onShowAdmin}
              className="text-white/80 hover:text-white smooth-transition touch-feedback flex items-center space-x-2"
            >
              <i className="fas fa-crown text-yellow-400"></i>
              <span>Admin</span>
            </button>
            <div className="text-white/60 text-sm hidden lg:block">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Online</span>
              </div>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              className="glass w-10 h-10 rounded-xl hover-glass smooth-transition p-0 touch-feedback"
              variant="ghost"
            >
              <i className={`fas ${showMobileMenu ? 'fa-times' : 'fa-bars'} text-white`}></i>
            </Button>
          </div>

          {/* Right Side - Notifications and User Menu */}
          <div className="hidden md:flex items-center space-x-2 sm:space-x-4">
            {/* Quick Stats */}
            <div className="hidden lg:flex items-center space-x-4 text-white/60 text-sm">
              <div className="flex items-center space-x-1">
                <i className="fas fa-tasks text-blue-400"></i>
                <span>5 Active</span>
              </div>
              <div className="flex items-center space-x-1">
                <i className="fas fa-check-circle text-green-400"></i>
                <span>12 Done</span>
              </div>
            </div>

            {/* Notifications */}
            <div className="relative">
              <Button
                onClick={() => setShowNotifications(!showNotifications)}
                className="glass w-10 h-10 rounded-xl hover-glass smooth-transition p-0 touch-feedback"
                variant="ghost"
              >
                <i className="fas fa-bell text-white"></i>
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center animate-pulse">
                  3
                </span>
              </Button>
            </div>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-2 sm:space-x-3 touch-feedback smooth-transition hover:bg-white/10 rounded-xl p-1 sm:p-2"
              >
                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-xl bg-gradient-to-br from-[hsl(var(--primary))] to-[hsl(var(--secondary))] flex items-center justify-center pulse-glow">
                  <span className="text-white font-semibold text-xs sm:text-sm">
                    {getInitials(user?.displayName || user?.email || "U")}
                  </span>
                </div>
                <div className="hidden sm:block text-left">
                  <div className="text-white text-sm font-medium">
                    {user?.displayName || "User"}
                  </div>
                  <div className="text-white/50 text-xs">
                    {user?.email?.split('@')[0]}
                  </div>
                </div>
                <i className="fas fa-chevron-down text-white/60 text-xs hidden sm:block"></i>
              </button>

              {/* User Dropdown Menu */}
              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-48 glass rounded-xl p-2 border border-white/20 slide-up">
                  <div className="space-y-1">
                    <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-2">
                      <i className="fas fa-user"></i>
                      <span>Profile</span>
                    </button>
                    <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-2">
                      <i className="fas fa-cog"></i>
                      <span>Settings</span>
                    </button>
                    <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-2">
                      <i className="fas fa-moon"></i>
                      <span>Dark Mode</span>
                    </button>
                    <hr className="border-white/20 my-1" />
                    <button
                      onClick={handleSignOut}
                      className="w-full text-left px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg smooth-transition flex items-center space-x-2"
                    >
                      <i className="fas fa-sign-out-alt"></i>
                      <span>Sign Out</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {showMobileMenu && (
          <div className="md:hidden border-t-2 border-purple-500/30 mt-3 pt-3 slide-up">
            <div className="space-y-2">
              <button
                onClick={() => {
                  setShowSearch(true);
                  setShowMobileMenu(false);
                }}
                className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-3"
              >
                <i className="fas fa-search"></i>
                <span>Search</span>
                <span className="ml-auto text-xs text-white/40">Ctrl+K</span>
              </button>
              <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-3">
                <i className="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
              </button>
              <button
                onClick={() => {
                  setShowFeatures(true);
                  setShowMobileMenu(false);
                }}
                className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-3"
              >
                <i className="fas fa-magic text-purple-400"></i>
                <span>Features</span>
                <span className="ml-auto text-xs text-white/40">Ctrl+F</span>
              </button>
              <button
                onClick={() => {
                  onShowAdmin();
                  setShowMobileMenu(false);
                }}
                className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-3"
              >
                <i className="fas fa-crown text-yellow-400"></i>
                <span>Admin Panel</span>
              </button>
              <button
                onClick={() => {
                  setShowNotifications(!showNotifications);
                  setShowMobileMenu(false);
                }}
                className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <i className="fas fa-bell"></i>
                  <span>Notifications</span>
                </div>
                <span className="w-5 h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center animate-pulse">3</span>
              </button>
              <hr className="border-white/20 my-2" />
              <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-3">
                <i className="fas fa-user"></i>
                <span>Profile</span>
              </button>
              <button className="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg smooth-transition flex items-center space-x-3">
                <i className="fas fa-cog"></i>
                <span>Settings</span>
              </button>
              <button
                onClick={handleSignOut}
                className="w-full text-left px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg smooth-transition flex items-center space-x-3"
              >
                <i className="fas fa-sign-out-alt"></i>
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        )}
      </nav>

      {showNotifications && (
        <NotificationPanel onClose={() => setShowNotifications(false)} />
      )}

      <FeatureSidebar
        isOpen={showFeatures}
        onClose={() => setShowFeatures(false)}
      />

      <SearchModal
        isOpen={showSearch}
        onClose={() => setShowSearch(false)}
      />
    </>
  );
}
