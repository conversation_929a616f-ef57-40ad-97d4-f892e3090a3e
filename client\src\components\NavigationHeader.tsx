import { useState } from "react";
import { signOutUser } from "@/lib/firebase";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import NotificationPanel from "./NotificationPanel";
import ProjectsPanel from "./ProjectsPanel";
import TeamCollaboration from "./TeamCollaboration";

interface NavigationHeaderProps {
  onShowAdmin: () => void;
}

export default function NavigationHeader({ onShowAdmin }: NavigationHeaderProps) {
  const { user } = useAuth();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProjects, setShowProjects] = useState(false);
  const [showTeamChat, setShowTeamChat] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOutUser();
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const getInitials = (name: string | null) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <>
      <nav className="glass-dark p-4 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 glass rounded-xl flex items-center justify-center">
              <i className="fas fa-tasks text-white"></i>
            </div>
            <h1 className="text-white font-bold text-xl">Cyber Community Task</h1>
          </div>

          <div className="hidden md:flex items-center space-x-6">
            <a href="#" className="text-white/80 hover:text-white smooth-transition">
              Dashboard
            </a>
            <button
              onClick={() => setShowProjects(true)}
              className="text-white/80 hover:text-white smooth-transition"
            >
              Projects
            </button>
            <button
              onClick={() => setShowTeamChat(true)}
              className="text-white/80 hover:text-white smooth-transition"
            >
              Team
            </button>
            <button
              onClick={onShowAdmin}
              className="text-white/80 hover:text-white smooth-transition"
            >
              Admin
            </button>
          </div>

          <div className="flex items-center space-x-4">
            <div className="relative">
              <Button
                onClick={() => setShowNotifications(!showNotifications)}
                className="glass w-10 h-10 rounded-xl hover-glass smooth-transition p-0"
                variant="ghost"
              >
                <i className="fas fa-bell text-white"></i>
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                  3
                </span>
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-[hsl(var(--primary))] to-[hsl(var(--secondary))] flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {getInitials(user?.displayName || user?.email || "U")}
                </span>
              </div>
              <Button
                onClick={handleSignOut}
                variant="ghost"
                className="text-white/60 hover:text-white p-2"
              >
                <i className="fas fa-sign-out-alt"></i>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {showNotifications && (
        <NotificationPanel onClose={() => setShowNotifications(false)} />
      )}
      
      {showProjects && (
        <ProjectsPanel onClose={() => setShowProjects(false)} />
      )}
      
      {showTeamChat && (
        <TeamCollaboration onClose={() => setShowTeamChat(false)} />
      )}
    </>
  );
}
