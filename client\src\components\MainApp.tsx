import { useState } from "react";
import NavigationHeader from "./NavigationHeader";
import TodoSection from "./TodoSection";
import MessagesBroadcast from "./MessagesBroadcast";
import QuickStats from "./QuickStats";
import AdminPanel from "./AdminPanel";

export default function MainApp() {
  const [showAdmin, setShowAdmin] = useState(false);

  return (
    <div className="min-h-screen gradient-bg-alt">
      <NavigationHeader onShowAdmin={() => setShowAdmin(true)} />

      <div className="max-w-7xl mx-auto p-4 lg:p-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <TodoSection />

          <div className="space-y-6">
            <MessagesBroadcast />
            <QuickStats />
          </div>
        </div>
      </div>

      {showAdmin && <AdminPanel onClose={() => setShowAdmin(false)} />}
    </div>
  );
}
