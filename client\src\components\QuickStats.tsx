import { useTasks } from "@/hooks/useFirestore";

export default function QuickStats() {
  const { tasks, loading } = useTasks();

  const stats = {
    totalTasks: tasks.length,
    completedTasks: tasks.filter(task => task.status === "completed").length,
    inProgressTasks: tasks.filter(task => task.status === "active").length,
    overdueTasks: tasks.filter(task => {
      if (!task.dueDate || task.status === "completed") return false;
      const dueDate = task.dueDate.toDate ? task.dueDate.toDate() : new Date(task.dueDate);
      return dueDate < new Date();
    }).length,
  };

  const completionPercentage = stats.totalTasks > 0 
    ? Math.round((stats.completedTasks / stats.totalTasks) * 100)
    : 0;

  if (loading) {
    return (
      <div className="glass rounded-3xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Quick Stats</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex justify-between">
              <div className="h-4 bg-white/10 rounded w-1/2"></div>
              <div className="h-4 bg-white/10 rounded w-8"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="glass rounded-3xl p-6">
      <h3 className="text-xl font-bold text-white mb-4">Quick Stats</h3>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-white/80">Total Tasks</span>
          <span className="text-white font-bold text-lg">{stats.totalTasks}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-white/80">Completed</span>
          <span className="text-green-400 font-bold text-lg">{stats.completedTasks}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-white/80">In Progress</span>
          <span className="text-yellow-400 font-bold text-lg">{stats.inProgressTasks}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-white/80">Overdue</span>
          <span className="text-red-400 font-bold text-lg">{stats.overdueTasks}</span>
        </div>
      </div>

      <div className="mt-6">
        <div className="flex items-center justify-between text-sm text-white/60 mb-2">
          <span>Progress</span>
          <span>{completionPercentage}%</span>
        </div>
        <div className="w-full glass rounded-full h-2">
          <div
            className="bg-gradient-to-r from-[hsl(var(--primary))] to-[hsl(var(--secondary))] h-2 rounded-full transition-all duration-500"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
}
